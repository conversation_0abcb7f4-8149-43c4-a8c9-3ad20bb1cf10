"use client";

import React, { createContext, useContext, ReactNode } from "react";
import {
  useCertificates as useBaseCertificates,
  type Certificate,
} from "@/hooks/use-certificates";

interface CertificatesContextType {
  certificates: Certificate[];
  isLoading: boolean;
  error: string | null;
  lastFetch: Date | null;
  refetch: () => Promise<void>;
  addCertificate: (
    certificate: Omit<Certificate, "id" | "userId" | "createdAt" | "updatedAt">
  ) => Promise<void>;
  updateCertificate: (
    id: string,
    updates: Partial<Certificate>
  ) => Promise<void>;
  deleteCertificate: (id: string) => Promise<void>;
  toggleFavorite: (id: string) => Promise<void>;
}

const CertificatesContext = createContext<CertificatesContextType | undefined>(
  undefined
);

interface CertificatesProviderProps {
  children: ReactNode;
}

export function CertificatesProvider({ children }: CertificatesProviderProps) {
  const certificatesData = useBaseCertificates();

  return (
    <CertificatesContext.Provider value={certificatesData}>
      {children}
    </CertificatesContext.Provider>
  );
}

export function useCertificates(): CertificatesContextType {
  const context = useContext(CertificatesContext);
  if (context === undefined) {
    throw new Error(
      "useCertificates must be used within a CertificatesProvider"
    );
  }
  return context;
}

// Re-export the hook for components that need it
export { useFilteredCertificates } from "@/hooks/use-certificates";
export type {
  Certificate,
  FilterType,
  SortField,
  SortOrder,
} from "@/hooks/use-certificates";

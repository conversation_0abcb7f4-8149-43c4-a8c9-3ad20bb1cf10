// Test database functions directly
const fs = require("fs");
const path = require("path");

// Load environment variables
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, "..", ".env.local");
    const envContent = fs.readFileSync(envPath, "utf8");

    envContent.split("\n").forEach((line) => {
      const [key, ...valueParts] = line.split("=");
      if (key && valueParts.length > 0) {
        const value = valueParts
          .join("=")
          .trim()
          .replace(/^"(.*)"$/, "$1");
        process.env[key.trim()] = value;
      }
    });
  } catch (error) {
    console.error("Failed to load .env.local:", error.message);
  }
}

loadEnvFile();

async function testDatabaseFunctions() {
  console.log("🧪 Testing Database Functions for Certificates");
  console.log("==============================================\n");

  try {
    const { neon } = require("@neondatabase/serverless");
    const sql = neon(
      process.env.DATABASE_URL_UNPOOLED || process.env.DATABASE_URL
    );

    const testUserId = "test-user-" + Date.now();

    console.log("1️⃣ Setting up test user and certificates...");

    // Create test user
    await sql`
      INSERT INTO "User" (id, email, name, password, "createdAt", "updatedAt")
      VALUES (${testUserId}, '<EMAIL>', 'Test User', 'test-password', NOW(), NOW());
    `;

    // Create test certificates with different statuses
    const cert1Id = "cert-1-" + Date.now();
    const cert2Id = "cert-2-" + Date.now();
    const cert3Id = "cert-3-" + Date.now();

    await sql`
      INSERT INTO "Certificate" (
        id, name, "issuingAuthority", "certificateNumber",
        "dateIssued", "expiryDate", "isFavorite",
        "createdAt", "updatedAt", "userId"
      ) VALUES
      (
        ${cert1Id},
        'STCW Basic Safety Training',
        'Maritime Safety Authority',
        'BST-2023-12345',
        NOW() - INTERVAL '1 year',
        NOW() + INTERVAL '1 year',
        true,
        NOW(),
        NOW(),
        ${testUserId}
      ),
      (
        ${cert2Id},
        'Medical First Aid',
        'Maritime Medical Institute',
        'MFA-2022-54321',
        NOW() - INTERVAL '2 years',
        NOW() + INTERVAL '30 days',
        false,
        NOW(),
        NOW(),
        ${testUserId}
      ),
      (
        ${cert3Id},
        'Advanced Firefighting',
        'Maritime Safety Authority',
        'AFF-2021-45678',
        NOW() - INTERVAL '3 years',
        NOW() - INTERVAL '30 days',
        false,
        NOW(),
        NOW(),
        ${testUserId}
      );
    `;

    console.log("✅ Created test user and 3 certificates");

    console.log("\n2️⃣ Testing basic certificate retrieval...");

    // Test getting all certificates
    const allCerts = await sql`
      SELECT * FROM "Certificate"
      WHERE "userId" = ${testUserId}
      ORDER BY name ASC;
    `;
    console.log(`✅ Retrieved ${allCerts.length} certificates`);

    allCerts.forEach((cert) => {
      console.log(`   - ${cert.name} (Favorite: ${cert.isFavorite})`);
    });

    console.log("\n3️⃣ Testing filtering by favorites...");

    const favoriteCerts = await sql`
      SELECT * FROM "Certificate"
      WHERE "userId" = ${testUserId} AND "isFavorite" = true
      ORDER BY name ASC;
    `;
    console.log(`✅ Retrieved ${favoriteCerts.length} favorite certificates`);

    console.log("\n4️⃣ Testing search functionality...");

    const searchResults = await sql`
      SELECT * FROM "Certificate"
      WHERE "userId" = ${testUserId}
      AND (
        name ILIKE ${"%STCW%"} OR
        "certificateNumber" ILIKE ${"%STCW%"} OR
        "issuingAuthority" ILIKE ${"%STCW%"}
      )
      ORDER BY name ASC;
    `;
    console.log(
      `✅ Search for 'STCW' returned ${searchResults.length} certificates`
    );

    console.log("\n5️⃣ Testing status-based filtering...");

    const today = new Date();
    const ninetyDaysFromNow = new Date();
    ninetyDaysFromNow.setDate(today.getDate() + 90);

    // Expiring soon (within 90 days)
    const expiringSoon = await sql`
      SELECT * FROM "Certificate"
      WHERE "userId" = ${testUserId}
      AND "expiryDate" >= ${today}
      AND "expiryDate" <= ${ninetyDaysFromNow}
      ORDER BY "expiryDate" ASC;
    `;
    console.log(`✅ Found ${expiringSoon.length} certificates expiring soon`);

    // Expired certificates
    const expired = await sql`
      SELECT * FROM "Certificate"
      WHERE "userId" = ${testUserId}
      AND "expiryDate" < ${today}
      ORDER BY "expiryDate" DESC;
    `;
    console.log(`✅ Found ${expired.length} expired certificates`);

    console.log("\n6️⃣ Testing sorting...");

    // Sort by expiry date
    const sortedByExpiry = await sql`
      SELECT * FROM "Certificate"
      WHERE "userId" = ${testUserId}
      ORDER BY "expiryDate" ASC NULLS LAST;
    `;
    console.log("✅ Sorted by expiry date:");
    sortedByExpiry.forEach((cert) => {
      const expiryStr = cert.expiryDate
        ? cert.expiryDate.toISOString().split("T")[0]
        : "No Expiry";
      console.log(`   - ${cert.name}: ${expiryStr}`);
    });

    console.log("\n7️⃣ Testing certificate updates...");

    // Update a certificate
    await sql`
      UPDATE "Certificate"
      SET "isFavorite" = true, "updatedAt" = NOW()
      WHERE id = ${cert2Id} AND "userId" = ${testUserId};
    `;

    // Toggle favorite status
    await sql`
      UPDATE "Certificate"
      SET "isFavorite" = NOT "isFavorite", "updatedAt" = NOW()
      WHERE id = ${cert3Id} AND "userId" = ${testUserId};
    `;

    console.log("✅ Updated certificate favorite statuses");

    // Verify updates
    const updatedCerts = await sql`
      SELECT id, name, "isFavorite" FROM "Certificate"
      WHERE "userId" = ${testUserId}
      ORDER BY name ASC;
    `;
    console.log("Updated favorite statuses:");
    updatedCerts.forEach((cert) => {
      console.log(`   - ${cert.name}: ${cert.isFavorite ? "⭐" : "☆"}`);
    });

    console.log("\n8️⃣ Testing status computation (frontend logic)...");

    function computeCertificateStatus(expiryDate) {
      if (!expiryDate) return "active";

      const today = new Date();
      const diffTime = expiryDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < 0) return "expired";
      if (diffDays <= 90) return "expiring-soon";
      return "active";
    }

    const certsWithStatus = allCerts.map((cert) => ({
      ...cert,
      status: computeCertificateStatus(cert.expiryDate),
    }));

    console.log("Certificate statuses:");
    certsWithStatus.forEach((cert) => {
      const statusEmoji = {
        active: "✅",
        "expiring-soon": "⚠️",
        expired: "❌",
      };
      console.log(
        `   ${statusEmoji[cert.status]} ${cert.name}: ${cert.status}`
      );
    });

    console.log("\n9️⃣ Testing API-like operations...");

    // Simulate the API endpoint logic
    const searchQuery = "Medical";
    const filter = "all";
    const sortBy = "name";
    const sortOrder = "asc";

    let query = `
      SELECT * FROM "Certificate"
      WHERE "userId" = $1
    `;
    const params = [testUserId];

    if (searchQuery) {
      query += ` AND (
        name ILIKE $${params.length + 1} OR
        "certificateNumber" ILIKE $${params.length + 1} OR
        "issuingAuthority" ILIKE $${params.length + 1}
      )`;
      params.push(`%${searchQuery}%`);
    }

    if (filter === "favorites") {
      query += ` AND "isFavorite" = true`;
    }

    query += ` ORDER BY ${
      sortBy === "name"
        ? "name"
        : sortBy === "dateIssued"
        ? '"dateIssued"'
        : '"expiryDate"'
    } ${sortOrder.toUpperCase()} NULLS LAST`;

    const apiResults = await sql.unsafe(query, params);
    console.log(
      `✅ API-like query for '${searchQuery}' returned ${apiResults.length} results`
    );

    console.log("\n🧹 Cleaning up test data...");
    await sql`DELETE FROM "Certificate" WHERE "userId" = ${testUserId};`;
    await sql`DELETE FROM "User" WHERE id = ${testUserId};`;
    console.log("✅ Test data cleaned up");

    console.log("\n🎉 Database function testing completed successfully!");
    console.log("\n📋 Test Results Summary:");
    console.log("✅ Basic CRUD operations work");
    console.log("✅ Filtering by favorites works");
    console.log("✅ Search functionality works");
    console.log("✅ Status-based filtering works");
    console.log("✅ Sorting works");
    console.log("✅ Status computation works");
    console.log("✅ API-like queries work");
    console.log("\n🚀 Database is ready for API integration!");
  } catch (error) {
    console.error("\n❌ Database function test failed:", error);
    console.error("Stack trace:", error.stack);
  }
}

testDatabaseFunctions();

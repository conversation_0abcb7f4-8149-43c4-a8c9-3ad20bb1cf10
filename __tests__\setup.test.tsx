import React from 'react'
import { render, screen } from '@testing-library/react'

// Simple test to verify Jest setup is working
describe('Test Setup', () => {
  it('should render a simple component', () => {
    const TestComponent = () => <div>Hello Test</div>
    
    render(<TestComponent />)
    
    expect(screen.getByText('Hello Test')).toBeInTheDocument()
  })

  it('should handle mock functions', () => {
    const mockFn = jest.fn()
    
    mockFn('test')
    
    expect(mockFn).toHaveBeenCalledWith('test')
  })

  it('should have fetch mocked', () => {
    expect(global.fetch).toBeDefined()
    expect(jest.isMockFunction(global.fetch)).toBe(true)
  })
})

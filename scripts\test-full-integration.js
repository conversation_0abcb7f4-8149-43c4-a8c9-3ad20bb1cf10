// Test the full integration: Authentication + API + Database
const fs = require('fs');
const path = require('path');

// Load environment variables
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local');
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim().replace(/^"(.*)"$/, '$1');
        process.env[key.trim()] = value;
      }
    });
  } catch (error) {
    console.error('Failed to load .env.local:', error.message);
  }
}

loadEnvFile();

async function testFullIntegration() {
  console.log('🧪 Testing Full Integration: Auth + API + Database');
  console.log('==================================================\n');

  const baseUrl = 'http://localhost:3000';
  const testEmail = '<EMAIL>';
  const testPassword = 'demo123';

  try {
    console.log('1️⃣ Testing login API...');
    
    // Test login
    const loginResponse = await fetch(`${baseUrl}/api/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail,
        password: testPassword,
      }),
    });

    if (!loginResponse.ok) {
      const error = await loginResponse.text();
      throw new Error(`Login failed: ${error}`);
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful');
    console.log(`   User: ${loginData.user.name} (${loginData.user.email})`);
    console.log(`   User ID: ${loginData.user.id}`);

    // Extract session cookie
    const setCookieHeader = loginResponse.headers.get('set-cookie');
    if (!setCookieHeader) {
      throw new Error('No session cookie received');
    }

    const sessionCookie = setCookieHeader.split(';')[0]; // Get just the session=value part
    console.log('✅ Session cookie received');

    console.log('\n2️⃣ Testing certificates API with authentication...');

    // Test GET /api/certificates
    const certsResponse = await fetch(`${baseUrl}/api/certificates`, {
      headers: {
        'Cookie': sessionCookie
      }
    });

    if (!certsResponse.ok) {
      const error = await certsResponse.text();
      throw new Error(`Failed to fetch certificates: ${error}`);
    }

    const certificates = await certsResponse.json();
    console.log(`✅ Retrieved ${certificates.length} certificates`);

    if (certificates.length > 0) {
      console.log('Sample certificates:');
      certificates.slice(0, 3).forEach(cert => {
        const favorite = cert.isFavorite ? '⭐' : '☆';
        const expiry = cert.expiryDate ? new Date(cert.expiryDate).toISOString().split('T')[0] : 'No Expiry';
        console.log(`   ${favorite} ${cert.name} (Expires: ${expiry})`);
      });
    }

    console.log('\n3️⃣ Testing filtered API requests...');

    // Test search
    const searchResponse = await fetch(`${baseUrl}/api/certificates?search=STCW`, {
      headers: {
        'Cookie': sessionCookie
      }
    });

    if (searchResponse.ok) {
      const searchResults = await searchResponse.json();
      console.log(`✅ Search for 'STCW' returned ${searchResults.length} results`);
    }

    // Test favorites filter
    const favoritesResponse = await fetch(`${baseUrl}/api/certificates?filter=favorites`, {
      headers: {
        'Cookie': sessionCookie
      }
    });

    if (favoritesResponse.ok) {
      const favoriteResults = await favoritesResponse.json();
      console.log(`✅ Favorites filter returned ${favoriteResults.length} results`);
    }

    // Test sorting
    const sortedResponse = await fetch(`${baseUrl}/api/certificates?sortBy=name&sortOrder=asc`, {
      headers: {
        'Cookie': sessionCookie
      }
    });

    if (sortedResponse.ok) {
      const sortedResults = await sortedResponse.json();
      console.log(`✅ Sorted results returned ${sortedResults.length} certificates`);
      if (sortedResults.length > 0) {
        console.log(`   First certificate: ${sortedResults[0].name}`);
      }
    }

    console.log('\n4️⃣ Testing certificate creation...');

    // Test creating a new certificate
    const newCertData = {
      name: 'Test API Certificate',
      issuingAuthority: 'API Test Authority',
      certificateNumber: 'API-TEST-' + Date.now(),
      dateIssued: new Date().toISOString(),
      expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
      isFavorite: true,
      notes: 'Created via API test'
    };

    const createResponse = await fetch(`${baseUrl}/api/certificates`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': sessionCookie
      },
      body: JSON.stringify(newCertData)
    });

    if (createResponse.ok) {
      const createResult = await createResponse.json();
      console.log('✅ Certificate created successfully');
      console.log(`   New certificate ID: ${createResult.id}`);

      // Test updating the certificate
      console.log('\n5️⃣ Testing certificate update...');

      const updateResponse = await fetch(`${baseUrl}/api/certificates/${createResult.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': sessionCookie
        },
        body: JSON.stringify({
          notes: 'Updated via API test',
          isFavorite: false
        })
      });

      if (updateResponse.ok) {
        console.log('✅ Certificate updated successfully');
      }

      // Test favorite toggle
      console.log('\n6️⃣ Testing favorite toggle...');

      const favoriteResponse = await fetch(`${baseUrl}/api/certificates/${createResult.id}/favorite`, {
        method: 'POST',
        headers: {
          'Cookie': sessionCookie
        }
      });

      if (favoriteResponse.ok) {
        const favoriteResult = await favoriteResponse.json();
        console.log(`✅ Favorite toggled: ${favoriteResult.isFavorite}`);
      }

      // Clean up - delete the test certificate
      console.log('\n7️⃣ Cleaning up test certificate...');

      const deleteResponse = await fetch(`${baseUrl}/api/certificates/${createResult.id}`, {
        method: 'DELETE',
        headers: {
          'Cookie': sessionCookie
        }
      });

      if (deleteResponse.ok) {
        console.log('✅ Test certificate deleted');
      }
    }

    console.log('\n8️⃣ Testing logout...');

    const logoutResponse = await fetch(`${baseUrl}/api/logout`, {
      method: 'POST',
      headers: {
        'Cookie': sessionCookie
      }
    });

    if (logoutResponse.ok) {
      console.log('✅ Logout successful');
    }

    // Test that API is now unauthorized
    const unauthorizedResponse = await fetch(`${baseUrl}/api/certificates`, {
      headers: {
        'Cookie': sessionCookie
      }
    });

    if (unauthorizedResponse.status === 401) {
      console.log('✅ API correctly returns 401 after logout');
    }

    console.log('\n🎉 Full integration test completed successfully!');
    console.log('\n📋 Test Summary:');
    console.log('✅ Authentication works');
    console.log('✅ Session management works');
    console.log('✅ API endpoints work');
    console.log('✅ Database operations work');
    console.log('✅ Filtering and search work');
    console.log('✅ CRUD operations work');
    console.log('✅ Authorization works');
    
    console.log('\n🚀 Ready for frontend testing!');
    console.log('\nNext steps:');
    console.log('1. Start development server: npm run dev');
    console.log('2. Navigate to: http://localhost:3000/login-alt');
    console.log(`3. Login with: ${testEmail} / ${testPassword}`);
    console.log('4. Go to: http://localhost:3000/certificates');
    console.log('5. Test all the enhanced features!');

  } catch (error) {
    console.error('\n❌ Full integration test failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 Tip: Make sure your development server is running:');
      console.log('   npm run dev');
    }
  }
}

testFullIntegration();

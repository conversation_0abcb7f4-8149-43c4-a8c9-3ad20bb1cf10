import {
  Eye,
  Download,
  Star,
  Calendar,
  Award,
  Clock,
  Edit,
  Trash2,
  Share2,
} from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useState } from "react";
import {
  downloadCertificateDocument,
  getCertificateViewUrl,
  hasDownloadableDocument,
  openFileInNewTab,
  isFileViewable,
  viewAllCertificateFiles,
  downloadCertificateFilesAsZip,
  getCertificateFiles,
} from "@/lib/download-utils";
import { DeleteConfirmationDialog } from "@/components/delete-confirmation-dialog";
import { useCertificates } from "@/contexts/certificates-context";
import { useToast } from "@/hooks/use-toast";

interface CertificateCardProps {
  cert: {
    id: string;
    name: string;
    certificateNumber: string;
    dateIssued: Date;
    expiryDate: Date | null;
    isFavorite?: boolean;
    status: "active" | "expired" | "expiring-soon";
    issuingAuthority: string;
    documentUrl?: string | null;
    documentName?: string | null;
    documentSize?: string | null;
    documentType?: string | null;
  };
  onView: (id: string) => void;
}

export function CertificateCard({ cert, onView }: CertificateCardProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isTogglingFavorite, setIsTogglingFavorite] = useState(false);

  const { toggleFavorite, deleteCertificate } = useCertificates();
  const { toast } = useToast();

  const formatDate = (date: Date | null) => {
    if (!date) return "No Expiry";
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  const handleDownload = async () => {
    if (!hasDownloadableDocument(cert) || isDownloading) return;

    setIsDownloading(true);
    try {
      // Try to get all files for this certificate
      const files = await getCertificateFiles(cert.id);

      if (files.length > 1) {
        // Multiple files - download as ZIP
        await downloadCertificateFilesAsZip(cert.id);
      } else if (files.length === 1) {
        // Single file - use direct download
        await downloadCertificateDocument(cert.id);
      } else {
        // Fallback to legacy download
        await downloadCertificateDocument(cert.id);
      }
    } catch (error) {
      console.error("Download failed:", error);
      // You could add a toast notification here
    } finally {
      setIsDownloading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            Active
          </Badge>
        );
      case "expiring-soon":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            Expiring Soon
          </Badge>
        );
      case "expired":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            Expired
          </Badge>
        );
      default:
        return null;
    }
  };

  const getDaysUntilExpiry = () => {
    if (!cert.expiryDate) return null;
    const today = new Date();
    const expiryDate = new Date(cert.expiryDate);
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysRemaining = getDaysUntilExpiry();

  const handleToggleFavorite = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isTogglingFavorite) return; // Prevent multiple clicks

    setIsTogglingFavorite(true);
    const wasFavorite = cert.isFavorite;

    try {
      await toggleFavorite(cert.id);

      // Show success toast
      toast({
        title: wasFavorite ? "Removed from favorites" : "Added to favorites",
        description: `${cert.name} has been ${
          wasFavorite ? "removed from" : "added to"
        } your favorites.`,
      });
    } catch (error) {
      console.error("Failed to toggle favorite:", error);
      toast({
        title: "Error",
        description: "Failed to update favorite status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsTogglingFavorite(false);
    }
  };

  const handleViewFiles = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!hasDownloadableDocument(cert)) return;

    try {
      // Try to get all files for this certificate
      const files = await getCertificateFiles(cert.id);

      if (files.length > 1) {
        // Multiple files - open all in new tabs
        await viewAllCertificateFiles(cert.id);
      } else if (files.length === 1) {
        // Single file - check if viewable
        const file = files[0];
        if (isFileViewable(file.fileName, file.fileType)) {
          openFileInNewTab(file.fileUrl);
        } else {
          // If not viewable, download instead
          await downloadCertificateDocument(cert.id);
        }
      } else {
        // Fallback to legacy view
        const viewData = await getCertificateViewUrl(cert.id);
        if (isFileViewable(viewData.fileName, viewData.fileType)) {
          openFileInNewTab(viewData.viewUrl);
        } else {
          // If not viewable, download instead
          await downloadCertificateDocument(cert.id);
        }
      }
    } catch (error) {
      console.error("View failed:", error);
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await deleteCertificate(cert.id);
      setShowDeleteDialog(false);
    } catch (error) {
      console.error("Delete failed:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCardClick = () => {
    onView(cert.id);
  };

  return (
    <TooltipProvider>
      <Card
        className="hover:shadow-md transition-shadow group overflow-hidden border-2 hover:border-primary/20 active:scale-[0.98] cursor-pointer"
        onClick={handleCardClick}
      >
        <CardHeader className="pb-2 relative p-4 sm:p-6">
          <div className="absolute right-3 top-3 sm:right-4 sm:top-4 flex gap-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-9 w-9 sm:h-8 sm:w-8 hover:text-yellow-500"
                  onClick={handleToggleFavorite}
                  disabled={isTogglingFavorite}
                >
                  <Star
                    className={`h-4 w-4 transition-colors ${
                      cert.isFavorite ? "text-yellow-500 fill-yellow-500" : ""
                    } ${isTogglingFavorite ? "opacity-50" : ""}`}
                  />
                  <span className="sr-only">Favorite</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {isTogglingFavorite
                  ? "Updating..."
                  : cert.isFavorite
                  ? "Remove from favorites"
                  : "Add to favorites"}
              </TooltipContent>
            </Tooltip>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-9 w-9 sm:h-8 sm:w-8"
                >
                  <span className="sr-only">Open menu</span>
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                    />
                  </svg>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onView(cert.id)}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onView(cert.id);
                  }}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    handleViewFiles(e);
                  }}
                  disabled={!hasDownloadableDocument(cert)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  {hasDownloadableDocument(cert) ? "View File" : "No File"}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDownload();
                  }}
                  disabled={!hasDownloadableDocument(cert) || isDownloading}
                >
                  <Download className="h-4 w-4 mr-2" />
                  {isDownloading ? "Downloading..." : "Download"}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowDeleteDialog(true);
                  }}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <CardTitle className="text-base sm:text-lg pr-16 sm:pr-20 leading-tight">
            {cert.name}
          </CardTitle>
          <div className="flex justify-between items-center mt-2">
            <span className="text-xs sm:text-sm text-muted-foreground flex items-center">
              <Award className="h-3.5 w-3.5 mr-1 inline flex-shrink-0" />
              <span className="truncate">{cert.certificateNumber}</span>
            </span>
            {getStatusBadge(cert.status)}
          </div>
        </CardHeader>

        <CardContent className="p-4 sm:p-6">
          <div className="text-sm space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex items-center">
                <Calendar className="h-3.5 w-3.5 mr-1" />
                Issued:
              </span>
              <span>{formatDate(cert.dateIssued)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground flex items-center">
                <Clock className="h-3.5 w-3.5 mr-1" />
                Expires:
              </span>
              <span
                className={
                  cert.status === "expired" ? "text-red-600 font-medium" : ""
                }
              >
                {formatDate(cert.expiryDate)}
              </span>
            </div>
            {cert.status !== "expired" && daysRemaining !== null && (
              <div className="mt-2 text-xs">
                <div className="w-full bg-gray-200 rounded-full h-1.5">
                  <div
                    className={`h-1.5 rounded-full ${
                      cert.status === "expiring-soon"
                        ? "bg-yellow-500"
                        : "bg-green-500"
                    }`}
                    style={{
                      width: `${Math.min(
                        100,
                        Math.max(0, (daysRemaining / 365) * 100)
                      )}%`,
                    }}
                  ></div>
                </div>
                <div className="flex justify-between mt-1">
                  <span
                    className={`${
                      cert.status === "expiring-soon"
                        ? "text-yellow-700"
                        : "text-green-700"
                    }`}
                  >
                    {daysRemaining > 0
                      ? `${daysRemaining} days remaining`
                      : "Expired"}
                  </span>
                </div>
              </div>
            )}
          </div>
        </CardContent>

        <CardFooter className="pt-0 pb-4 sm:pb-6 px-4 sm:px-6 flex justify-between gap-2">
          <Button
            variant="outline"
            size="default"
            className="h-10 flex-1"
            onClick={handleViewFiles}
            disabled={!hasDownloadableDocument(cert)}
          >
            <Eye className="h-4 w-4 mr-2" />
            <span className="hidden xs:inline">
              {hasDownloadableDocument(cert) ? "View File" : "No File"}
            </span>
            <span className="xs:hidden">
              {hasDownloadableDocument(cert) ? "View" : "No File"}
            </span>
          </Button>
          <Button
            variant="ghost"
            size="default"
            className="h-10 flex-1 text-muted-foreground"
            onClick={(e) => {
              e.stopPropagation();
              handleDownload();
            }}
            disabled={!hasDownloadableDocument(cert) || isDownloading}
          >
            <Download className="h-4 w-4 mr-2" />
            <span className="hidden xs:inline">
              {isDownloading
                ? "Downloading..."
                : hasDownloadableDocument(cert)
                ? "Download"
                : "No Document"}
            </span>
            <span className="xs:hidden">
              {isDownloading
                ? "..."
                : hasDownloadableDocument(cert)
                ? "Download"
                : "None"}
            </span>
          </Button>
        </CardFooter>
      </Card>

      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleDelete}
        title={cert.name}
        isDeleting={isDeleting}
      />
    </TooltipProvider>
  );
}

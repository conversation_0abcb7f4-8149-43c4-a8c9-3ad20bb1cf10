// Create a test user with sample certificates for testing
const fs = require('fs');
const path = require('path');
const { hash } = require('bcrypt');

// Load environment variables
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local');
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim().replace(/^"(.*)"$/, '$1');
        process.env[key.trim()] = value;
      }
    });
  } catch (error) {
    console.error('Failed to load .env.local:', error.message);
  }
}

loadEnvFile();

async function createTestUser() {
  console.log('🧪 Creating Test User and Sample Certificates');
  console.log('============================================\n');

  try {
    const { neon } = require("@neondatabase/serverless");
    const sql = neon(process.env.DATABASE_URL_UNPOOLED || process.env.DATABASE_URL);
    
    const testUserId = 'test-user-demo';
    const testEmail = '<EMAIL>';
    const testPassword = 'demo123';
    
    console.log('1️⃣ Creating test user...');
    
    // Check if user already exists
    const existingUser = await sql`
      SELECT id FROM "User" WHERE email = ${testEmail} LIMIT 1;
    `;
    
    if (existingUser.length > 0) {
      console.log('⚠️  Test user already exists, cleaning up first...');
      await sql`DELETE FROM "Certificate" WHERE "userId" = ${existingUser[0].id};`;
      await sql`DELETE FROM "User" WHERE email = ${testEmail};`;
    }
    
    // Hash password
    const hashedPassword = await hash(testPassword, 10);
    
    // Create test user
    await sql`
      INSERT INTO "User" (id, email, name, password, "createdAt", "updatedAt")
      VALUES (${testUserId}, ${testEmail}, 'Demo User', ${hashedPassword}, NOW(), NOW());
    `;
    
    console.log('✅ Test user created:');
    console.log(`   Email: ${testEmail}`);
    console.log(`   Password: ${testPassword}`);
    console.log(`   User ID: ${testUserId}`);

    console.log('\n2️⃣ Creating sample certificates...');
    
    // Create diverse sample certificates
    const certificates = [
      {
        id: 'cert-stcw-basic',
        name: 'STCW Basic Safety Training',
        issuingAuthority: 'Maritime Safety Authority',
        certificateNumber: 'BST-2023-12345',
        dateIssued: new Date('2023-01-15'),
        expiryDate: new Date('2025-01-15'),
        isFavorite: true,
        notes: 'Completed at Maritime Training Center'
      },
      {
        id: 'cert-medical-first-aid',
        name: 'Medical First Aid',
        issuingAuthority: 'Maritime Medical Institute',
        certificateNumber: 'MFA-2022-54321',
        dateIssued: new Date('2022-08-22'),
        expiryDate: new Date('2024-08-22'),
        isFavorite: false,
        notes: 'Includes CPR and basic medical training'
      },
      {
        id: 'cert-survival-craft',
        name: 'Proficiency in Survival Craft',
        issuingAuthority: 'Nautical Training Center',
        certificateNumber: 'PSC-2023-98765',
        dateIssued: new Date('2023-03-22'),
        expiryDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
        isFavorite: false,
        notes: 'Lifeboat and life raft operations'
      },
      {
        id: 'cert-firefighting',
        name: 'Advanced Firefighting',
        issuingAuthority: 'Maritime Safety Authority',
        certificateNumber: 'AFF-2021-45678',
        dateIssued: new Date('2021-09-05'),
        expiryDate: new Date('2023-09-05'), // Expired
        isFavorite: false,
        notes: 'Advanced fire suppression techniques'
      },
      {
        id: 'cert-gmdss',
        name: 'GMDSS Radio Operator',
        issuingAuthority: 'Communications Authority',
        certificateNumber: 'GMDSS-2021-87654',
        dateIssued: new Date('2021-06-15'),
        expiryDate: new Date('2026-06-15'),
        isFavorite: true,
        notes: 'Global Maritime Distress and Safety System'
      },
      {
        id: 'cert-security-officer',
        name: 'Ship Security Officer',
        issuingAuthority: 'Maritime Security Institute',
        certificateNumber: 'SSO-2022-13579',
        dateIssued: new Date('2022-05-10'),
        expiryDate: new Date('2027-05-10'),
        isFavorite: false,
        notes: 'ISPS Code compliance training'
      },
      {
        id: 'cert-radar-observer',
        name: 'Radar Observer Certificate',
        issuingAuthority: 'Navigation Institute',
        certificateNumber: 'ROC-2023-24680',
        dateIssued: new Date('2023-11-20'),
        expiryDate: null, // No expiry
        isFavorite: true,
        notes: 'Radar plotting and collision avoidance'
      }
    ];
    
    for (const cert of certificates) {
      await sql`
        INSERT INTO "Certificate" (
          id, name, "issuingAuthority", "certificateNumber", 
          "dateIssued", "expiryDate", "isFavorite", notes,
          "createdAt", "updatedAt", "userId"
        ) VALUES (
          ${cert.id}, 
          ${cert.name}, 
          ${cert.issuingAuthority}, 
          ${cert.certificateNumber}, 
          ${cert.dateIssued}, 
          ${cert.expiryDate},
          ${cert.isFavorite},
          ${cert.notes},
          NOW(), 
          NOW(), 
          ${testUserId}
        );
      `;
    }
    
    console.log(`✅ Created ${certificates.length} sample certificates`);

    console.log('\n3️⃣ Verifying data...');
    
    // Verify certificates were created
    const createdCerts = await sql`
      SELECT name, "isFavorite", "expiryDate" FROM "Certificate" 
      WHERE "userId" = ${testUserId}
      ORDER BY name ASC;
    `;
    
    console.log('Sample certificates:');
    createdCerts.forEach(cert => {
      const favorite = cert.isFavorite ? '⭐' : '☆';
      const expiry = cert.expiryDate ? cert.expiryDate.toISOString().split('T')[0] : 'No Expiry';
      console.log(`   ${favorite} ${cert.name} (Expires: ${expiry})`);
    });

    console.log('\n4️⃣ Testing status computation...');
    
    function computeCertificateStatus(expiryDate) {
      if (!expiryDate) return "active";
      
      const today = new Date();
      const diffTime = expiryDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays < 0) return "expired";
      if (diffDays <= 90) return "expiring-soon";
      return "active";
    }
    
    const statusCounts = { active: 0, 'expiring-soon': 0, expired: 0 };
    createdCerts.forEach(cert => {
      const status = computeCertificateStatus(cert.expiryDate);
      statusCounts[status]++;
    });
    
    console.log('Certificate status distribution:');
    console.log(`   ✅ Active: ${statusCounts.active}`);
    console.log(`   ⚠️  Expiring Soon: ${statusCounts['expiring-soon']}`);
    console.log(`   ❌ Expired: ${statusCounts.expired}`);

    console.log('\n🎉 Test user and sample data created successfully!');
    console.log('\n📋 Login Instructions:');
    console.log('1. Start your development server: npm run dev');
    console.log('2. Navigate to: http://localhost:3000/login-alt');
    console.log(`3. Login with: ${testEmail} / ${testPassword}`);
    console.log('4. Go to: http://localhost:3000/certificates');
    console.log('5. Test the enhanced features:');
    console.log('   - Search for "STCW" or "Medical"');
    console.log('   - Filter by "Favorites" or "Expiring Soon"');
    console.log('   - Sort by name, date issued, or expiry date');
    console.log('   - Use keyboard shortcuts: ⌘K for search, ⌘N for new');
    
    console.log('\n🚀 Ready to test the full database integration!');

  } catch (error) {
    console.error('\n❌ Failed to create test user:', error);
    console.error('Stack trace:', error.stack);
  }
}

createTestUser();

# 🧪 Manual Testing Guide - File Upload & Download

## 🚀 Getting Started

1. **Start the development server**:
   ```bash
   npm run dev
   # or
   node_modules\.bin\next.cmd dev
   ```

2. **Open the application**: http://localhost:3000

3. **Login with test credentials**:
   - Email: `<EMAIL>`
   - Password: `demo123`

## 📋 Test Scenarios

### **Test 1: File Upload During Certificate Creation**

1. **Navigate to**: `/certificates/new`
2. **Fill in certificate details**:
   - Name: "Test Certificate with File"
   - Issuing Authority: "Test Authority"
   - Certificate Number: "TEST-001"
   - Date Issued: Today's date
   - Expiry Date: One year from now
3. **Upload a file**:
   - Click the upload area or drag & drop
   - Try different file types: PDF, JPG, PNG
   - Verify file size limit (8MB max)
4. **Submit the form**
5. **Expected Results**:
   - ✅ File uploads successfully
   - ✅ File name appears in upload area
   - ✅ Certificate is created
   - ✅ Redirected to certificates list

### **Test 2: Download from Certificate List (Grid View)**

1. **Navigate to**: `/certificates`
2. **Ensure Grid view is selected**
3. **Find a certificate with a document**
4. **Test download options**:
   - Click the dropdown menu (three dots)
   - Click "Download" option
   - Try the footer "Download" button
5. **Expected Results**:
   - ✅ Download button is enabled for certificates with files
   - ✅ Download button is disabled for certificates without files
   - ✅ File downloads with correct filename
   - ✅ Loading states work properly

### **Test 3: Download from Certificate List (Table View)**

1. **Navigate to**: `/certificates`
2. **Switch to Table view**
3. **Find a certificate with a document**
4. **Click the download icon** in the actions column
5. **Expected Results**:
   - ✅ Download icon is enabled for certificates with files
   - ✅ Download icon is disabled for certificates without files
   - ✅ Tooltip shows appropriate message
   - ✅ File downloads correctly

### **Test 4: Download from Certificate Detail Page**

1. **Navigate to a certificate detail page**: `/certificates/[id]`
2. **Check the document section**
3. **Test download functionality**:
   - Click "Download" button in document section
   - Try "Download Certificate" in actions section
4. **Expected Results**:
   - ✅ File metadata is displayed (name, size, type)
   - ✅ Download button works correctly
   - ✅ File downloads with proper filename

### **Test 5: Mobile Responsiveness**

1. **Open browser developer tools**
2. **Switch to mobile view** (375px width)
3. **Test all download functionality**:
   - Certificate list (grid and table)
   - Certificate detail page
   - File upload process
4. **Expected Results**:
   - ✅ Download buttons are touch-friendly (44px min)
   - ✅ UI adapts properly to mobile screens
   - ✅ All functionality works on mobile

### **Test 6: Error Scenarios**

1. **Test file size limit**:
   - Try uploading a file larger than 8MB
   - Expected: Error message displayed
2. **Test unsupported file types**:
   - Try uploading .txt, .doc, or other unsupported files
   - Expected: Error message displayed
3. **Test network issues**:
   - Simulate slow network during upload
   - Expected: Loading states work properly
4. **Test missing files**:
   - Try downloading from a certificate without a file
   - Expected: Download button is disabled

### **Test 7: File Type Support**

Test each supported file type:
- **PDF files**: Upload and download
- **JPG files**: Upload and download  
- **JPEG files**: Upload and download
- **PNG files**: Upload and download

**Expected Results**:
- ✅ All file types upload successfully
- ✅ File type icons display correctly
- ✅ Downloads maintain original format
- ✅ File metadata is preserved

## 🔍 What to Look For

### **During Upload**:
- [ ] Upload progress indicator
- [ ] File name appears after upload
- [ ] File can be removed before submission
- [ ] Error messages for invalid files
- [ ] Form submission includes file data

### **In Certificate Lists**:
- [ ] Download buttons appear for certificates with files
- [ ] Download buttons are disabled for certificates without files
- [ ] Loading states during download
- [ ] Proper tooltips and labels
- [ ] Mobile-friendly touch targets

### **In Certificate Detail**:
- [ ] File metadata is displayed (name, size, type)
- [ ] Download button works correctly
- [ ] File type icon is appropriate
- [ ] "No document" state for certificates without files

### **Download Behavior**:
- [ ] Files download with correct filenames
- [ ] File content is intact and viewable
- [ ] Download works in different browsers
- [ ] No authentication errors
- [ ] Proper error handling for missing files

## 🐛 Common Issues to Check

1. **File not downloading**: Check browser's download settings
2. **Authentication errors**: Ensure user is logged in
3. **File size errors**: Verify file is under 8MB
4. **Upload failures**: Check network connection and file type
5. **Mobile issues**: Test on actual mobile devices

## 📊 Success Criteria

**All tests pass if**:
- ✅ Files upload successfully during certificate creation
- ✅ File metadata is stored and displayed correctly
- ✅ Download buttons work in all views (grid, table, detail)
- ✅ Downloads work for all supported file types
- ✅ Mobile experience is smooth and responsive
- ✅ Error scenarios are handled gracefully
- ✅ Security works (users can only access their own files)

## 🎯 Performance Checks

- **Upload speed**: Files should upload within reasonable time
- **Download speed**: Downloads should start immediately
- **UI responsiveness**: No blocking during file operations
- **Memory usage**: No memory leaks during repeated operations

---

**Testing Status**: Ready for manual validation
**Estimated Testing Time**: 30-45 minutes for complete test suite

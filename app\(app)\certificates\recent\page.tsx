"use client";

import Link from "next/link";
import { Calendar, Edit, Eye, FileText, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CertificateStatusBadge } from "@/components/certificate-status-badge";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function RecentCertificatesPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Sample data
  const recentCertificates = [
    {
      id: "1",
      name: "STCW Basic Safety Training",
      issuingAuthority: "Maritime Safety Authority",
      certificateNumber: "BST-2023-12345",
      dateIssued: new Date("2023-01-15"),
      expiryDate: new Date("2025-01-15"),
      dateAdded: new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    },
    {
      id: "3",
      name: "Proficiency in Survival Craft",
      issuingAuthority: "Nautical Training Center",
      certificateNumber: "PSC-2023-98765",
      dateIssued: new Date("2023-03-22"),
      expiryDate: new Date("2025-03-22"),
      dateAdded: new Date(new Date().getTime() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
    },
  ];

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await fetch("/api/certificates", {
          credentials: "include",
        });

        if (!response.ok) {
          if (response.status === 401) {
            router.push("/login");
            return;
          }
        }

        // Simulate loading data
        setTimeout(() => {
          setIsLoading(false);
        }, 500);
      } catch (error) {
        console.error("Auth check failed:", error);
        router.push("/login");
      }
    };

    checkAuth();
  }, [router]);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getTimeAgo = (date: Date) => {
    const now = new Date();
    const diffTime = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return "Today";
    if (diffDays === 1) return "Yesterday";
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return formatDate(date);
  };

  if (isLoading) {
    return (
      <div className="p-6 text-center">
        <div className="animate-pulse">Loading recent certificates...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="mt-4 text-center">
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Recently Added Certificates
          </h1>
          <p className="text-muted-foreground">
            Certificates you've added in the last 7 days
          </p>
        </div>
        <Button asChild>
          <Link href="/certificates/new">
            <Plus className="mr-2 h-4 w-4" /> Add New Certificate
          </Link>
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {recentCertificates.map((certificate) => (
          <Card key={certificate.id}>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg">{certificate.name}</CardTitle>
                <CertificateStatusBadge expiryDate={certificate.expiryDate} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center text-sm text-muted-foreground">
                  <Calendar className="mr-2 h-4 w-4" />
                  Added {getTimeAgo(certificate.dateAdded)}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <FileText className="mr-2 h-4 w-4" />
                  {certificate.certificateNumber}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex gap-2">
              <Button size="sm" variant="outline" asChild>
                <Link href={`/certificates/${certificate.id}`}>
                  <Eye className="mr-1 h-3 w-3" /> View
                </Link>
              </Button>
              <Button size="sm" asChild>
                <Link href={`/certificates/${certificate.id}/edit`}>
                  <Edit className="mr-1 h-3 w-3" /> Edit
                </Link>
              </Button>
            </CardFooter>
          </Card>
        ))}

        {recentCertificates.length === 0 && (
          <div className="text-center py-12 col-span-full">
            <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-medium">No Recent Certificates</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              You haven't added any certificates in the last 7 days.
            </p>
            <Button className="mt-4" asChild>
              <Link href="/certificates/new">
                <Plus className="mr-2 h-4 w-4" /> Add New Certificate
              </Link>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

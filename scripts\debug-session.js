// Debug session cookie handling
const fs = require('fs');
const path = require('path');

// Load environment variables
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local');
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim().replace(/^"(.*)"$/, '$1');
        process.env[key.trim()] = value;
      }
    });
  } catch (error) {
    console.error('Failed to load .env.local:', error.message);
  }
}

loadEnvFile();

async function debugSession() {
  console.log('🔍 Debugging Session Cookie Handling');
  console.log('===================================\n');

  const baseUrl = 'http://localhost:3000';
  const testEmail = '<EMAIL>';
  const testPassword = 'demo123';

  try {
    console.log('1️⃣ Testing login and cookie details...');
    
    const loginResponse = await fetch(`${baseUrl}/api/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail,
        password: testPassword,
      }),
    });

    console.log(`Login response status: ${loginResponse.status}`);
    
    if (!loginResponse.ok) {
      const error = await loginResponse.text();
      console.error('❌ Login failed:', error);
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful');
    console.log(`   User: ${loginData.user.name}`);

    // Examine all response headers
    console.log('\n📋 Response Headers:');
    for (const [key, value] of loginResponse.headers.entries()) {
      console.log(`   ${key}: ${value}`);
    }

    // Extract and analyze the session cookie
    const setCookieHeader = loginResponse.headers.get('set-cookie');
    console.log('\n🍪 Set-Cookie Header:');
    console.log(`   ${setCookieHeader}`);

    if (!setCookieHeader) {
      console.error('❌ No set-cookie header found!');
      return;
    }

    // Parse cookie details
    const cookieParts = setCookieHeader.split(';').map(part => part.trim());
    console.log('\n🔍 Cookie Parts:');
    cookieParts.forEach(part => {
      console.log(`   ${part}`);
    });

    const sessionCookie = cookieParts[0]; // Get just the session=value part
    console.log(`\n🎯 Session Cookie: ${sessionCookie}`);

    // Test the session cookie with API
    console.log('\n2️⃣ Testing API with session cookie...');
    
    const apiResponse = await fetch(`${baseUrl}/api/certificates`, {
      headers: {
        'Cookie': sessionCookie
      }
    });

    console.log(`API response status: ${apiResponse.status}`);
    
    if (apiResponse.ok) {
      const certificates = await apiResponse.json();
      console.log(`✅ API call successful - ${certificates.length} certificates`);
    } else {
      const error = await apiResponse.text();
      console.log(`❌ API call failed: ${error}`);
    }

    console.log('\n3️⃣ Testing session parsing...');
    
    // Test session parsing manually
    try {
      const sessionValue = sessionCookie.split('=')[1];
      console.log(`Session value: ${sessionValue}`);
      
      const sessionData = JSON.parse(decodeURIComponent(sessionValue));
      console.log('✅ Session data parsed successfully:');
      console.log(`   User ID: ${sessionData.user.id}`);
      console.log(`   User Email: ${sessionData.user.email}`);
      console.log(`   User Name: ${sessionData.user.name}`);
    } catch (parseError) {
      console.error('❌ Failed to parse session data:', parseError.message);
    }

    console.log('\n4️⃣ Testing direct database query...');
    
    // Test if the user exists in database
    const { neon } = require("@neondatabase/serverless");
    const sql = neon(process.env.DATABASE_URL_UNPOOLED || process.env.DATABASE_URL);
    
    const user = await sql`
      SELECT id, email, name FROM "User" WHERE email = ${testEmail} LIMIT 1;
    `;
    
    if (user.length > 0) {
      console.log('✅ User found in database:');
      console.log(`   ID: ${user[0].id}`);
      console.log(`   Email: ${user[0].email}`);
      console.log(`   Name: ${user[0].name}`);
      
      // Check certificates for this user
      const certs = await sql`
        SELECT COUNT(*) as count FROM "Certificate" WHERE "userId" = ${user[0].id};
      `;
      console.log(`   Certificates: ${certs[0].count}`);
    } else {
      console.error('❌ User not found in database!');
    }

  } catch (error) {
    console.error('\n❌ Debug session failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 Tip: Make sure your development server is running:');
      console.log('   npm run dev');
    }
  }
}

debugSession();

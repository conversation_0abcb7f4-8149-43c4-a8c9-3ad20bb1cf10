"use server"

import { nanoid } from "nanoid"
import { revalidatePath } from "next/cache"
import { redirect } from "next/navigation"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { createCertificate, updateCertificate, deleteCertificate } from "@/lib/db"

export async function addCertificate(formData: FormData) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return { error: "You must be logged in to add a certificate" }
  }

  const name = formData.get("name") as string
  const issuingAuthority = formData.get("authority") as string
  const certificateNumber = formData.get("number") as string
  const dateIssuedStr = formData.get("dateIssued") as string
  const expiryDateStr = formData.get("expiryDate") as string
  const noExpiry = formData.get("noExpiry") === "on"
  const notes = formData.get("notes") as string

  if (!name || !issuingAuthority || !certificateNumber || !dateIssuedStr) {
    return { error: "Missing required fields" }
  }

  const dateIssued = new Date(dateIssuedStr)
  let expiryDate: Date | null = null

  if (!noExpiry && expiryDateStr) {
    expiryDate = new Date(expiryDateStr)
  }

  try {
    await createCertificate({
      id: nanoid(),
      name,
      issuingAuthority,
      certificateNumber,
      dateIssued,
      expiryDate,
      notes: notes || null,
      documentUrl: null, // We'll handle file uploads separately
      userId: session.user.id,
    })

    revalidatePath("/certificates")
    redirect("/certificates")
  } catch (error) {
    console.error("Error adding certificate:", error)
    return { error: "Failed to add certificate. Please try again." }
  }
}

export async function updateCertificateAction(id: string, formData: FormData) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return { error: "You must be logged in to update a certificate" }
  }

  const name = formData.get("name") as string
  const issuingAuthority = formData.get("authority") as string
  const certificateNumber = formData.get("number") as string
  const dateIssuedStr = formData.get("dateIssued") as string
  const expiryDateStr = formData.get("expiryDate") as string
  const noExpiry = formData.get("noExpiry") === "on"
  const notes = formData.get("notes") as string

  if (!name || !issuingAuthority || !certificateNumber || !dateIssuedStr) {
    return { error: "Missing required fields" }
  }

  const dateIssued = new Date(dateIssuedStr)
  let expiryDate: Date | null = null

  if (!noExpiry && expiryDateStr) {
    expiryDate = new Date(expiryDateStr)
  }

  try {
    await updateCertificate(id, session.user.id, {
      name,
      issuingAuthority,
      certificateNumber,
      dateIssued,
      expiryDate,
      notes: notes || null,
    })

    revalidatePath("/certificates")
    redirect("/certificates")
  } catch (error) {
    console.error("Error updating certificate:", error)
    return { error: "Failed to update certificate. Please try again." }
  }
}

export async function deleteCertificateAction(id: string) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return { error: "You must be logged in to delete a certificate" }
  }

  try {
    await deleteCertificate(id, session.user.id)
    revalidatePath("/certificates")
    return { success: true }
  } catch (error) {
    console.error("Error deleting certificate:", error)
    return { error: "Failed to delete certificate. Please try again." }
  }
}

"use client";

import { useState } from "react";
import { getYear, getMonth } from "date-fns";

import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface EnhancedCalendarProps {
  mode?: "single";
  selected?: Date;
  onSelect?: (date: Date | undefined) => void;
  initialFocus?: boolean;
  disabled?: boolean;
  month?: Date;
  onMonthChange?: (month: Date) => void;
}

export function EnhancedCalendar({
  mode = "single",
  selected,
  onSelect,
  initialFocus,
  disabled,
  month,
  onMonthChange,
}: EnhancedCalendarProps) {
  const [currentMonth, setCurrentMonth] = useState<Date>(
    month || selected || new Date()
  );

  // Generate year options (current year ± 50 years)
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: 101 }, (_, i) => currentYear - 50 + i);

  // Month options
  const monthOptions = [
    { value: 0, label: "January" },
    { value: 1, label: "February" },
    { value: 2, label: "March" },
    { value: 3, label: "April" },
    { value: 4, label: "May" },
    { value: 5, label: "June" },
    { value: 6, label: "July" },
    { value: 7, label: "August" },
    { value: 8, label: "September" },
    { value: 9, label: "October" },
    { value: 10, label: "November" },
    { value: 11, label: "December" },
  ];

  const handleYearChange = (year: string) => {
    const newDate = new Date(parseInt(year), getMonth(currentMonth), 1);
    setCurrentMonth(newDate);
    onMonthChange?.(newDate);
  };

  const handleMonthChange = (month: string) => {
    const newDate = new Date(getYear(currentMonth), parseInt(month), 1);
    setCurrentMonth(newDate);
    onMonthChange?.(newDate);
  };

  const handleMonthChangeFromCalendar = (newMonth: Date) => {
    setCurrentMonth(newMonth);
    onMonthChange?.(newMonth);
  };

  return (
    <div>
      {/* Year/Month Selectors */}
      <div className="flex gap-2 p-3 border-b">
        <Select
          value={getMonth(currentMonth).toString()}
          onValueChange={handleMonthChange}
        >
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {monthOptions.map((month) => (
              <SelectItem key={month.value} value={month.value.toString()}>
                {month.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select
          value={getYear(currentMonth).toString()}
          onValueChange={handleYearChange}
        >
          <SelectTrigger className="w-20">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {yearOptions.map((year) => (
              <SelectItem key={year} value={year.toString()}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      {/* Calendar */}
      <Calendar
        mode={mode}
        selected={selected}
        onSelect={onSelect}
        month={currentMonth}
        onMonthChange={handleMonthChangeFromCalendar}
        initialFocus={initialFocus}
        disabled={disabled}
      />
    </div>
  );
}

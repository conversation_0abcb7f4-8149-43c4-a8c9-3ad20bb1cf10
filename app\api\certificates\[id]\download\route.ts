import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { getCertificateById } from "@/lib/db"

// Helper function to get user from session cookie
async function getUserFromSession() {
  try {
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get("session")
    if (!sessionCookie) {
      return null
    }

    const session = JSON.parse(sessionCookie.value)
    return session.user
  } catch (error) {
    console.error("Error parsing session:", error)
    return null
  }
}

// GET /api/certificates/[id]/download - Download certificate document
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = await params
    const certificate = await getCertificateById(id, user.id)

    if (!certificate) {
      return NextResponse.json({ error: "Certificate not found" }, { status: 404 })
    }

    if (!certificate.documentUrl) {
      return NextResponse.json({ error: "No document available for this certificate" }, { status: 404 })
    }

    // Fetch the file from Uploadthing and serve it with download headers
    try {
      const fileResponse = await fetch(certificate.documentUrl)

      if (!fileResponse.ok) {
        return NextResponse.json({ error: "Document file not found or corrupted" }, { status: 404 })
      }

      const fileBuffer = await fileResponse.arrayBuffer()
      const fileName = certificate.documentName || `certificate-${certificate.name}.pdf`

      // Determine content type
      let contentType = certificate.documentType || 'application/octet-stream'
      if (!contentType && fileName.toLowerCase().endsWith('.pdf')) {
        contentType = 'application/pdf'
      } else if (!contentType && fileName.toLowerCase().match(/\.(jpg|jpeg)$/)) {
        contentType = 'image/jpeg'
      } else if (!contentType && fileName.toLowerCase().endsWith('.png')) {
        contentType = 'image/png'
      }

      // Return the file with download headers to force download
      return new NextResponse(fileBuffer, {
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${fileName}"`,
          'Content-Length': fileBuffer.byteLength.toString(),
          'Cache-Control': 'private, no-cache',
        },
      })
    } catch (error) {
      console.error("Error fetching file:", error)
      return NextResponse.json({ error: "Unable to fetch document file" }, { status: 500 })
    }
  } catch (error) {
    console.error("Error downloading certificate:", error)
    return NextResponse.json(
      { error: "Failed to download certificate" },
      { status: 500 }
    )
  }
}

// Alternative implementation for proxying files through the server
// This provides more security but uses more server resources
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = await params
    const certificate = await getCertificateById(id, user.id)

    if (!certificate) {
      return NextResponse.json({ error: "Certificate not found" }, { status: 404 })
    }

    if (!certificate.documentUrl) {
      return NextResponse.json({ error: "No document available for this certificate" }, { status: 404 })
    }

    try {
      // Fetch the file from Uploadthing
      const fileResponse = await fetch(certificate.documentUrl)

      if (!fileResponse.ok) {
        return NextResponse.json({ error: "Document file not found or corrupted" }, { status: 404 })
      }

      const fileBuffer = await fileResponse.arrayBuffer()
      const fileName = certificate.documentName || `certificate-${certificate.name}.pdf`

      // Determine content type
      let contentType = certificate.documentType || 'application/octet-stream'
      if (!contentType && fileName.toLowerCase().endsWith('.pdf')) {
        contentType = 'application/pdf'
      } else if (!contentType && fileName.toLowerCase().match(/\.(jpg|jpeg)$/)) {
        contentType = 'image/jpeg'
      } else if (!contentType && fileName.toLowerCase().endsWith('.png')) {
        contentType = 'image/png'
      }

      // Return the file with appropriate headers
      return new NextResponse(fileBuffer, {
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${fileName}"`,
          'Content-Length': fileBuffer.byteLength.toString(),
          'Cache-Control': 'private, no-cache',
        },
      })
    } catch (error) {
      console.error("Error fetching file:", error)
      return NextResponse.json({ error: "Unable to fetch document file" }, { status: 500 })
    }
  } catch (error) {
    console.error("Error proxying certificate download:", error)
    return NextResponse.json(
      { error: "Failed to download certificate" },
      { status: 500 }
    )
  }
}

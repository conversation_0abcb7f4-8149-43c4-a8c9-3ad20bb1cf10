"use client";

import { useState, useRef } from "react";
import { Upload, FileText, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface FileUploadFallbackProps {
  onFileSelect: (file: File) => void;
  onFileRemove: () => void;
  uploadedFileName?: string | null;
  accept?: string;
  maxSize?: number; // in MB
  disabled?: boolean;
  id?: string;
  "aria-label"?: string;
  "aria-describedby"?: string;
}

export function FileUploadFallback({
  onFileSelect,
  onFileRemove,
  uploadedFileName,
  accept = ".pdf,.jpg,.jpeg,.png",
  maxSize = 10,
  disabled = false,
  id = "file-upload",
  "aria-label": ariaLabel = "Upload file",
  "aria-describedby": ariaDescribedBy,
}: FileUploadFallbackProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      return `File size must be less than ${maxSize}MB`;
    }

    // Check file type
    const allowedTypes = accept.split(",").map((type) => type.trim());
    const fileExtension =
      "." + (file.name.split(".").pop()?.toLowerCase() || "");
    const mimeType = file.type;

    // Define MIME type mappings for better validation
    const mimeTypeMap: Record<string, string[]> = {
      ".pdf": ["application/pdf"],
      ".jpg": ["image/jpeg"],
      ".jpeg": ["image/jpeg"],
      ".png": ["image/png"],
    };

    const isValidExtension = allowedTypes.some((type) => {
      if (type.startsWith(".")) {
        // Check both extension and MIME type
        const expectedMimeTypes = mimeTypeMap[type] || [];
        return (
          type === fileExtension &&
          (expectedMimeTypes.length === 0 ||
            expectedMimeTypes.includes(mimeType))
        );
      } else {
        // Direct MIME type check
        return type === mimeType;
      }
    });

    if (!isValidExtension) {
      return `File type not supported. Please upload: ${allowedTypes.join(
        ", "
      )}`;
    }

    return null;
  };

  const handleFileSelect = (file: File) => {
    setError(null);
    const validationError = validateFile(file);

    if (validationError) {
      setError(validationError);
      return;
    }

    onFileSelect(file);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Only set drag over to false if we're leaving the drop zone entirely
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;

    if (x < rect.left || x >= rect.right || y < rect.top || y >= rect.bottom) {
      setIsDragOver(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleClick = () => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.key === "Enter" || e.key === " ") && !disabled) {
      e.preventDefault();
      fileInputRef.current?.click();
    }
  };

  if (uploadedFileName) {
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between p-3 border rounded-lg bg-muted/50">
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">{uploadedFileName}</span>
          </div>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onFileRemove}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }

  const errorId = `${id}-error`;
  const descriptionId = `${id}-description`;

  return (
    <div className="space-y-2">
      <div
        className={`flex items-center justify-center w-full h-32 border-2 border-dashed rounded-lg transition-colors ${
          disabled
            ? "cursor-not-allowed opacity-50 border-muted-foreground/25 bg-muted/25"
            : isDragOver
            ? "border-primary bg-primary/5 cursor-pointer"
            : "border-muted-foreground/25 bg-muted/50 hover:bg-muted cursor-pointer"
        }`}
        onDragOver={handleDragOver}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        role="button"
        tabIndex={disabled ? -1 : 0}
        aria-label={ariaLabel}
        aria-describedby={`${descriptionId} ${ariaDescribedBy || ""}`.trim()}
        aria-disabled={disabled ? "true" : "false"}
      >
        <div className="flex flex-col items-center justify-center pt-5 pb-6">
          <Upload className="w-8 h-8 mb-2 text-muted-foreground" />
          <p className="mb-2 text-sm text-muted-foreground">
            <span className="font-semibold">Click to upload</span> or drag and
            drop
          </p>
          <p id={descriptionId} className="text-xs text-muted-foreground">
            PDF, JPG or PNG (max. {maxSize}MB)
          </p>
        </div>
      </div>

      <input
        ref={fileInputRef}
        id={id}
        type="file"
        accept={accept}
        onChange={handleInputChange}
        disabled={disabled}
        className="sr-only"
        aria-label={ariaLabel}
        aria-describedby={`${descriptionId} ${ariaDescribedBy || ""}`.trim()}
      />

      {error && (
        <Alert variant="destructive" id={errorId}>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}

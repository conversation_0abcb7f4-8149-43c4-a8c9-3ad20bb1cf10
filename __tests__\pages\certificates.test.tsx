import React from 'react'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import CertificatesPage from '@/app/(app)/certificates/page'
import {
  render,
  createMockCertificates,
  mockFetchSuccess,
  mockFetchError,
  cleanup,
} from '../utils/test-utils'

// Mock the certificates hook
jest.mock('@/hooks/use-certificates', () => ({
  useCertificates: jest.fn(),
  useFilteredCertificates: jest.fn(),
}))

// Mock components that might not be available in test environment
jest.mock('@/components/certificate-card', () => ({
  CertificateCard: ({ cert, onView }: any) => (
    <div data-testid={`certificate-card-${cert.id}`} onClick={() => onView(cert.id)}>
      {cert.name}
    </div>
  ),
}))

jest.mock('@/components/certificate-table', () => ({
  CertificateTable: ({ certificates, onView }: any) => (
    <div data-testid="certificate-table">
      {certificates.map((cert: any) => (
        <div key={cert.id} onClick={() => onView(cert.id)}>
          {cert.name}
        </div>
      ))}
    </div>
  ),
}))

jest.mock('@/components/certificates-skeleton', () => ({
  CertificatesPageSkeleton: () => <div data-testid="certificates-skeleton">Loading...</div>,
}))

const { useCertificates, useFilteredCertificates } = require('@/hooks/use-certificates')

describe('CertificatesPage', () => {
  const mockCertificates = createMockCertificates(5)
  const mockUseCertificates = {
    certificates: mockCertificates,
    isLoading: false,
    error: null,
  }
  const mockUseFilteredCertificates = {
    filteredCertificates: mockCertificates,
    counts: {
      all: 5,
      favorites: 2,
      expiringSoon: 1,
      expired: 1,
    },
  }

  beforeEach(() => {
    cleanup()
    useCertificates.mockReturnValue(mockUseCertificates)
    useFilteredCertificates.mockReturnValue(mockUseFilteredCertificates)
  })

  describe('Loading States', () => {
    it('shows skeleton when loading', () => {
      useCertificates.mockReturnValue({
        ...mockUseCertificates,
        isLoading: true,
      })

      render(<CertificatesPage />)

      expect(screen.getByTestId('certificates-skeleton')).toBeInTheDocument()
    })

    it('shows certificates when loaded', () => {
      render(<CertificatesPage />)

      expect(screen.queryByTestId('certificates-skeleton')).not.toBeInTheDocument()
      expect(screen.getByText('Certificate 1')).toBeInTheDocument()
    })

    it('shows error message when there is an error', () => {
      useCertificates.mockReturnValue({
        ...mockUseCertificates,
        error: 'Failed to load certificates',
      })

      render(<CertificatesPage />)

      expect(screen.getByText(/Failed to load certificates/)).toBeInTheDocument()
    })
  })

  describe('Action Cards', () => {
    it('displays correct counts in action cards', () => {
      render(<CertificatesPage />)

      expect(screen.getByText('5')).toBeInTheDocument() // All count
      expect(screen.getByText('2')).toBeInTheDocument() // Favorites count
      expect(screen.getByText('1')).toBeInTheDocument() // Expiring soon count
    })

    it('filters certificates when action card is clicked', async () => {
      const user = userEvent.setup()
      render(<CertificatesPage />)

      const favoritesCard = screen.getByText('Favorites').closest('div')
      await user.click(favoritesCard!)

      // Should update URL and trigger filtering
      expect(useFilteredCertificates).toHaveBeenCalledWith(
        mockCertificates,
        '',
        'favorites',
        'expiryDate',
        'asc'
      )
    })
  })

  describe('Search Functionality', () => {
    it('updates search query on input', async () => {
      const user = userEvent.setup()
      render(<CertificatesPage />)

      const searchInput = screen.getByPlaceholderText(/search certificates/i)
      await user.type(searchInput, 'test query')

      expect(searchInput).toHaveValue('test query')
    })

    it('debounces search input', async () => {
      const user = userEvent.setup()
      render(<CertificatesPage />)

      const searchInput = screen.getByPlaceholderText(/search certificates/i)
      
      // Type quickly
      await user.type(searchInput, 'test')
      
      // Should not immediately trigger filtering
      expect(useFilteredCertificates).toHaveBeenCalledWith(
        mockCertificates,
        'test',
        'all',
        'expiryDate',
        'asc'
      )
    })

    it('clears search when input is cleared', async () => {
      const user = userEvent.setup()
      render(<CertificatesPage />)

      const searchInput = screen.getByPlaceholderText(/search certificates/i)
      await user.type(searchInput, 'test')
      await user.clear(searchInput)

      expect(searchInput).toHaveValue('')
    })
  })

  describe('View Toggle', () => {
    it('switches between grid and table view', async () => {
      const user = userEvent.setup()
      render(<CertificatesPage />)

      // Should start in grid view
      expect(screen.getAllByTestId(/certificate-card/)).toHaveLength(5)

      // Switch to table view
      const tableViewButton = screen.getByRole('tab', { name: /table/i })
      await user.click(tableViewButton)

      expect(screen.getByTestId('certificate-table')).toBeInTheDocument()
    })
  })

  describe('Sorting', () => {
    it('changes sort order when dropdown is used', async () => {
      const user = userEvent.setup()
      render(<CertificatesPage />)

      const sortDropdown = screen.getByRole('button', { name: /sort/i })
      await user.click(sortDropdown)

      const nameOption = screen.getByText('Name')
      await user.click(nameOption)

      expect(useFilteredCertificates).toHaveBeenCalledWith(
        mockCertificates,
        '',
        'all',
        'name',
        'asc'
      )
    })

    it('toggles sort direction', async () => {
      const user = userEvent.setup()
      render(<CertificatesPage />)

      const sortOrderButton = screen.getByRole('button', { name: /order/i })
      await user.click(sortOrderButton)

      expect(useFilteredCertificates).toHaveBeenCalledWith(
        mockCertificates,
        '',
        'all',
        'expiryDate',
        'desc'
      )
    })
  })

  describe('Certificate Interaction', () => {
    it('navigates to certificate details when card is clicked', async () => {
      const user = userEvent.setup()
      const mockPush = jest.fn()
      
      // Mock useRouter
      jest.doMock('next/navigation', () => ({
        useRouter: () => ({ push: mockPush }),
        useSearchParams: () => new URLSearchParams(),
      }))

      render(<CertificatesPage />)

      const certificateCard = screen.getByTestId('certificate-card-cert-1')
      await user.click(certificateCard)

      expect(mockPush).toHaveBeenCalledWith('/certificates/cert-1')
    })
  })

  describe('Keyboard Shortcuts', () => {
    it('focuses search input on Ctrl+K', () => {
      render(<CertificatesPage />)

      const searchInput = screen.getByPlaceholderText(/search certificates/i)
      
      fireEvent.keyDown(document, { key: 'k', ctrlKey: true })

      expect(searchInput).toHaveFocus()
    })

    it('navigates to new certificate page on Ctrl+N', () => {
      const mockPush = jest.fn()
      
      jest.doMock('next/navigation', () => ({
        useRouter: () => ({ push: mockPush }),
        useSearchParams: () => new URLSearchParams(),
      }))

      render(<CertificatesPage />)

      fireEvent.keyDown(document, { key: 'n', ctrlKey: true })

      expect(mockPush).toHaveBeenCalledWith('/certificates/new')
    })
  })

  describe('Empty States', () => {
    it('shows empty state when no certificates match filter', () => {
      useFilteredCertificates.mockReturnValue({
        filteredCertificates: [],
        counts: {
          all: 5,
          favorites: 0,
          expiringSoon: 0,
          expired: 0,
        },
      })

      render(<CertificatesPage />)

      expect(screen.getByText(/no certificates found/i)).toBeInTheDocument()
    })

    it('shows different empty state for search vs filter', () => {
      useFilteredCertificates.mockReturnValue({
        filteredCertificates: [],
        counts: {
          all: 0,
          favorites: 0,
          expiringSoon: 0,
          expired: 0,
        },
      })

      render(<CertificatesPage />)

      expect(screen.getByText(/get started by adding/i)).toBeInTheDocument()
    })
  })

  describe('URL State Management', () => {
    it('initializes state from URL parameters', () => {
      // Mock URL with search params
      jest.doMock('next/navigation', () => ({
        useRouter: () => ({ push: jest.fn() }),
        useSearchParams: () => new URLSearchParams('?query=test&filter=favorites&sort=name&order=desc'),
      }))

      render(<CertificatesPage />)

      expect(useFilteredCertificates).toHaveBeenCalledWith(
        mockCertificates,
        'test',
        'favorites',
        'name',
        'desc'
      )
    })

    it('updates URL when filters change', async () => {
      const mockPush = jest.fn()
      
      jest.doMock('next/navigation', () => ({
        useRouter: () => ({ push: mockPush }),
        useSearchParams: () => new URLSearchParams(),
      }))

      const user = userEvent.setup()
      render(<CertificatesPage />)

      const favoritesCard = screen.getByText('Favorites').closest('div')
      await user.click(favoritesCard!)

      expect(mockPush).toHaveBeenCalledWith(
        expect.stringContaining('filter=favorites'),
        expect.objectContaining({ scroll: false })
      )
    })
  })

  describe('Performance', () => {
    it('does not make API calls when filtering locally', async () => {
      const user = userEvent.setup()
      mockFetchSuccess(mockCertificates)

      render(<CertificatesPage />)

      // Clear any initial fetch calls
      jest.clearAllMocks()

      // Perform local operations
      const searchInput = screen.getByPlaceholderText(/search certificates/i)
      await user.type(searchInput, 'test')

      const favoritesCard = screen.getByText('Favorites').closest('div')
      await user.click(favoritesCard!)

      // Should not make additional API calls
      expect(global.fetch).not.toHaveBeenCalled()
    })

    it('only loads data once on mount', () => {
      render(<CertificatesPage />)

      expect(useCertificates).toHaveBeenCalledWith()
      expect(useCertificates).toHaveBeenCalledTimes(1)
    })
  })

  describe('Responsive Design', () => {
    it('adapts layout for mobile screens', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })

      render(<CertificatesPage />)

      // Should still render all components but with mobile-friendly layout
      expect(screen.getByText('Certificates')).toBeInTheDocument()
      expect(screen.getByPlaceholderText(/search certificates/i)).toBeInTheDocument()
    })
  })
})

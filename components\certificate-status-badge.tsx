import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface CertificateStatusBadgeProps {
  expiryDate: Date | null
  className?: string
  showText?: boolean
}

export function CertificateStatusBadge({ expiryDate, className, showText = true }: CertificateStatusBadgeProps) {
  if (!expiryDate) {
    return (
      <Badge variant="outline" className={cn("border-gray-500 text-gray-500", className)}>
        {showText ? "No Expiry" : ""}
      </Badge>
    )
  }

  const today = new Date()
  const expiryDateObj = new Date(expiryDate)
  const daysRemaining = Math.ceil((expiryDateObj.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

  if (daysRemaining < 0) {
    return (
      <Badge variant="destructive" className={cn("bg-red-500 hover:bg-red-600", className)}>
        {showText ? "Expired" : ""}
      </Badge>
    )
  }

  if (daysRemaining <= 30) {
    return (
      <Badge variant="outline" className={cn("border-orange-500 text-orange-500 bg-orange-50", className)}>
        {showText ? "Expiring Soon" : ""}
      </Badge>
    )
  }

  if (daysRemaining <= 90) {
    return (
      <Badge variant="outline" className={cn("border-yellow-500 text-yellow-500 bg-yellow-50", className)}>
        {showText ? "Expiring Soon" : ""}
      </Badge>
    )
  }

  return (
    <Badge variant="outline" className={cn("border-green-500 text-green-500 bg-green-50", className)}>
      {showText ? "Active" : ""}
    </Badge>
  )
}

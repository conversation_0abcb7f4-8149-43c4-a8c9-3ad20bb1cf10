import React from 'react'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import {
  render,
  createMockCertificates,
  mockFetchSuccess,
  cleanup,
} from '../utils/test-utils'

// Mock the entire flow
const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
}

jest.mock('next/navigation', () => ({
  useRouter: () => mockRouter,
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/certificates',
}))

// Mock components
jest.mock('@/components/certificate-card', () => ({
  CertificateCard: ({ cert, onView }: any) => (
    <div data-testid={`certificate-card-${cert.id}`}>
      <h3>{cert.name}</h3>
      <p>{cert.status}</p>
      <button onClick={() => onView(cert.id)}>View</button>
    </div>
  ),
}))

jest.mock('@/components/certificate-table', () => ({
  CertificateTable: ({ certificates, onView }: any) => (
    <table data-testid="certificate-table">
      <tbody>
        {certificates.map((cert: any) => (
          <tr key={cert.id}>
            <td>{cert.name}</td>
            <td>
              <button onClick={() => onView(cert.id)}>View</button>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  ),
}))

describe('Certificates End-to-End Flow', () => {
  beforeEach(() => {
    cleanup()
    mockRouter.push.mockClear()
  })

  describe('Complete User Journey', () => {
    it('allows user to view, filter, search, and navigate certificates', async () => {
      const user = userEvent.setup()
      const mockCertificates = createMockCertificates(10)
      
      // Mock API responses
      mockFetchSuccess(mockCertificates)

      // Import and render the certificates page
      const CertificatesPage = (await import('@/app/(app)/certificates/page')).default
      render(<CertificatesPage />)

      // Wait for certificates to load
      await waitFor(() => {
        expect(screen.getByText('Certificate 1')).toBeInTheDocument()
      })

      // Test 1: Verify all certificates are displayed
      expect(screen.getAllByTestId(/certificate-card/)).toHaveLength(10)

      // Test 2: Test search functionality
      const searchInput = screen.getByPlaceholderText(/search certificates/i)
      await user.type(searchInput, 'Certificate 1')

      // Should filter to show only Certificate 1 and Certificate 10 (contains "1")
      await waitFor(() => {
        const visibleCards = screen.getAllByTestId(/certificate-card/)
        expect(visibleCards.length).toBeLessThanOrEqual(2)
      })

      // Test 3: Clear search and test filter by favorites
      await user.clear(searchInput)
      
      const favoritesCard = screen.getByText('Favorites').closest('[role="button"]')
      await user.click(favoritesCard!)

      // Should show only favorite certificates
      await waitFor(() => {
        const visibleCards = screen.getAllByTestId(/certificate-card/)
        expect(visibleCards.length).toBeGreaterThan(0)
      })

      // Test 4: Test view toggle
      const tableViewTab = screen.getByRole('tab', { name: /table/i })
      await user.click(tableViewTab)

      expect(screen.getByTestId('certificate-table')).toBeInTheDocument()
      expect(screen.queryByTestId(/certificate-card/)).not.toBeInTheDocument()

      // Test 5: Test certificate navigation
      const viewButton = screen.getAllByText('View')[0]
      await user.click(viewButton)

      expect(mockRouter.push).toHaveBeenCalledWith(expect.stringMatching(/\/certificates\/cert-\d+/))

      // Test 6: Test sorting
      const sortDropdown = screen.getByRole('button', { name: /sort/i })
      await user.click(sortDropdown)

      const nameOption = screen.getByText('Name')
      await user.click(nameOption)

      // Should trigger re-render with name sorting
      await waitFor(() => {
        expect(screen.getByTestId('certificate-table')).toBeInTheDocument()
      })
    })

    it('handles empty states correctly', async () => {
      // Mock empty response
      mockFetchSuccess([])

      const CertificatesPage = (await import('@/app/(app)/certificates/page')).default
      render(<CertificatesPage />)

      await waitFor(() => {
        expect(screen.getByText(/get started by adding/i)).toBeInTheDocument()
      })

      // Should show "Add Certificate" button
      expect(screen.getByText(/add your first certificate/i)).toBeInTheDocument()
    })

    it('handles loading and error states', async () => {
      const CertificatesPage = (await import('@/app/(app)/certificates/page')).default
      
      // Test loading state
      render(<CertificatesPage />)
      expect(screen.getByTestId('certificates-skeleton')).toBeInTheDocument()

      // Mock error response
      ;(global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'))

      // Re-render to trigger error
      render(<CertificatesPage />)

      await waitFor(() => {
        expect(screen.getByText(/failed to load certificates/i)).toBeInTheDocument()
      })
    })
  })

  describe('Performance Characteristics', () => {
    it('loads data only once and filters locally', async () => {
      const user = userEvent.setup()
      const mockCertificates = createMockCertificates(100) // Large dataset
      
      mockFetchSuccess(mockCertificates)

      const CertificatesPage = (await import('@/app/(app)/certificates/page')).default
      render(<CertificatesPage />)

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('Certificate 1')).toBeInTheDocument()
      })

      // Clear fetch mock to track subsequent calls
      jest.clearAllMocks()

      // Perform multiple filter operations
      const searchInput = screen.getByPlaceholderText(/search certificates/i)
      await user.type(searchInput, 'test')
      await user.clear(searchInput)

      const favoritesCard = screen.getByText('Favorites').closest('[role="button"]')
      await user.click(favoritesCard!)

      const allCard = screen.getByText('All').closest('[role="button"]')
      await user.click(allCard!)

      // Should not make any additional API calls
      expect(global.fetch).not.toHaveBeenCalled()
    })

    it('handles large datasets efficiently', async () => {
      const user = userEvent.setup()
      const largeMockCertificates = createMockCertificates(1000)
      
      mockFetchSuccess(largeMockCertificates)

      const CertificatesPage = (await import('@/app/(app)/certificates/page')).default
      
      const startTime = performance.now()
      render(<CertificatesPage />)

      await waitFor(() => {
        expect(screen.getByText('Certificate 1')).toBeInTheDocument()
      })

      // Test search performance
      const searchInput = screen.getByPlaceholderText(/search certificates/i)
      const searchStartTime = performance.now()
      
      await user.type(searchInput, 'Certificate 1')
      
      const searchEndTime = performance.now()
      const searchDuration = searchEndTime - searchStartTime

      // Search should be fast (under 100ms for 1000 items)
      expect(searchDuration).toBeLessThan(100)

      const endTime = performance.now()
      const totalDuration = endTime - startTime

      // Total render time should be reasonable
      expect(totalDuration).toBeLessThan(1000)
    })
  })

  describe('Accessibility', () => {
    it('supports keyboard navigation', async () => {
      const mockCertificates = createMockCertificates(5)
      mockFetchSuccess(mockCertificates)

      const CertificatesPage = (await import('@/app/(app)/certificates/page')).default
      render(<CertificatesPage />)

      await waitFor(() => {
        expect(screen.getByText('Certificate 1')).toBeInTheDocument()
      })

      // Test keyboard shortcuts
      const searchInput = screen.getByPlaceholderText(/search certificates/i)
      
      // Ctrl+K should focus search
      fireEvent.keyDown(document, { key: 'k', ctrlKey: true })
      expect(searchInput).toHaveFocus()

      // Ctrl+N should navigate to new certificate
      fireEvent.keyDown(document, { key: 'n', ctrlKey: true })
      expect(mockRouter.push).toHaveBeenCalledWith('/certificates/new')
    })

    it('has proper ARIA labels and roles', async () => {
      const mockCertificates = createMockCertificates(3)
      mockFetchSuccess(mockCertificates)

      const CertificatesPage = (await import('@/app/(app)/certificates/page')).default
      render(<CertificatesPage />)

      await waitFor(() => {
        expect(screen.getByText('Certificate 1')).toBeInTheDocument()
      })

      // Check for proper roles
      expect(screen.getByRole('searchbox')).toBeInTheDocument()
      expect(screen.getAllByRole('tab')).toHaveLength(2) // Grid and Table tabs
      expect(screen.getAllByRole('button')).toHaveLength(expect.any(Number))

      // Check for proper labels
      expect(screen.getByLabelText(/search certificates/i)).toBeInTheDocument()
    })
  })

  describe('URL State Management', () => {
    it('maintains state in URL and restores on page load', async () => {
      const user = userEvent.setup()
      const mockCertificates = createMockCertificates(5)
      mockFetchSuccess(mockCertificates)

      // Mock URL with existing state
      jest.doMock('next/navigation', () => ({
        useRouter: () => mockRouter,
        useSearchParams: () => new URLSearchParams('?query=test&filter=favorites&view=table'),
        usePathname: () => '/certificates',
      }))

      const CertificatesPage = (await import('@/app/(app)/certificates/page')).default
      render(<CertificatesPage />)

      await waitFor(() => {
        expect(screen.getByText('Certificate 1')).toBeInTheDocument()
      })

      // Should restore state from URL
      const searchInput = screen.getByPlaceholderText(/search certificates/i)
      expect(searchInput).toHaveValue('test')

      // Should be in table view
      expect(screen.getByTestId('certificate-table')).toBeInTheDocument()
    })

    it('updates URL when state changes', async () => {
      const user = userEvent.setup()
      const mockCertificates = createMockCertificates(5)
      mockFetchSuccess(mockCertificates)

      const CertificatesPage = (await import('@/app/(app)/certificates/page')).default
      render(<CertificatesPage />)

      await waitFor(() => {
        expect(screen.getByText('Certificate 1')).toBeInTheDocument()
      })

      // Change search
      const searchInput = screen.getByPlaceholderText(/search certificates/i)
      await user.type(searchInput, 'test')

      // Should update URL (debounced)
      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith(
          expect.stringContaining('query=test'),
          expect.objectContaining({ scroll: false })
        )
      }, { timeout: 1000 })
    })
  })

  describe('Error Recovery', () => {
    it('recovers gracefully from network errors', async () => {
      // Mock initial failure
      ;(global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'))

      const CertificatesPage = (await import('@/app/(app)/certificates/page')).default
      render(<CertificatesPage />)

      await waitFor(() => {
        expect(screen.getByText(/failed to load certificates/i)).toBeInTheDocument()
      })

      // Mock successful retry
      const mockCertificates = createMockCertificates(3)
      mockFetchSuccess(mockCertificates)

      // Trigger retry (if retry mechanism exists)
      const retryButton = screen.queryByText(/retry/i)
      if (retryButton) {
        const user = userEvent.setup()
        await user.click(retryButton)

        await waitFor(() => {
          expect(screen.getByText('Certificate 1')).toBeInTheDocument()
        })
      }
    })
  })
})

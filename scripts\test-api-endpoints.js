// Test API endpoints for certificates
const fs = require('fs');
const path = require('path');

// Load environment variables
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local');
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim().replace(/^"(.*)"$/, '$1');
        process.env[key.trim()] = value;
      }
    });
  } catch (error) {
    console.error('Failed to load .env.local:', error.message);
  }
}

loadEnvFile();

async function testApiEndpoints() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🧪 Testing Certificate API Endpoints');
  console.log('=====================================\n');

  try {
    // Test 1: Create a test user and certificate in the database directly
    console.log('1️⃣ Setting up test data...');
    
    const { neon } = require("@neondatabase/serverless");
    const sql = neon(process.env.DATABASE_URL_UNPOOLED || process.env.DATABASE_URL);
    
    const testUserId = 'test-user-' + Date.now();
    const testCertId = 'test-cert-' + Date.now();
    
    // Create test user
    await sql`
      INSERT INTO "User" (id, email, name, "createdAt", "updatedAt")
      VALUES (${testUserId}, '<EMAIL>', 'Test User', NOW(), NOW());
    `;
    
    // Create test certificate
    await sql`
      INSERT INTO "Certificate" (
        id, name, "issuingAuthority", "certificateNumber", 
        "dateIssued", "expiryDate", "isFavorite", 
        "createdAt", "updatedAt", "userId"
      ) VALUES (
        ${testCertId}, 
        'Test STCW Certificate', 
        'Maritime Authority', 
        'STCW-TEST-123', 
        NOW() - INTERVAL '1 year', 
        NOW() + INTERVAL '1 year',
        true,
        NOW(), 
        NOW(), 
        ${testUserId}
      );
    `;
    
    console.log('✅ Test data created');
    console.log(`   User ID: ${testUserId}`);
    console.log(`   Certificate ID: ${testCertId}`);

    // Test 2: Test GET /api/certificates (without authentication - should fail)
    console.log('\n2️⃣ Testing GET /api/certificates (no auth)...');
    
    try {
      const response = await fetch(`${baseUrl}/api/certificates`);
      console.log(`   Status: ${response.status}`);
      
      if (response.status === 401) {
        console.log('✅ Correctly returns 401 Unauthorized without session');
      } else {
        console.log('⚠️  Expected 401 but got:', response.status);
      }
    } catch (error) {
      console.log('❌ Fetch failed:', error.message);
    }

    // Test 3: Test with mock session cookie
    console.log('\n3️⃣ Testing GET /api/certificates (with mock session)...');
    
    try {
      const mockSession = JSON.stringify({ user: { id: testUserId } });
      const response = await fetch(`${baseUrl}/api/certificates`, {
        headers: {
          'Cookie': `session=${mockSession}`
        }
      });
      
      console.log(`   Status: ${response.status}`);
      
      if (response.ok) {
        const certificates = await response.json();
        console.log('✅ Successfully fetched certificates');
        console.log(`   Found ${certificates.length} certificate(s)`);
        
        if (certificates.length > 0) {
          const cert = certificates[0];
          console.log(`   Sample certificate: ${cert.name}`);
          console.log(`   Is favorite: ${cert.isFavorite}`);
          console.log(`   Status computed on frontend will be based on expiry: ${cert.expiryDate}`);
        }
      } else {
        const error = await response.text();
        console.log('❌ Failed to fetch certificates:', error);
      }
    } catch (error) {
      console.log('❌ Fetch failed:', error.message);
    }

    // Test 4: Test GET /api/certificates with filters
    console.log('\n4️⃣ Testing GET /api/certificates with filters...');
    
    try {
      const mockSession = JSON.stringify({ user: { id: testUserId } });
      const response = await fetch(`${baseUrl}/api/certificates?filter=favorites&sortBy=name&sortOrder=asc`, {
        headers: {
          'Cookie': `session=${mockSession}`
        }
      });
      
      console.log(`   Status: ${response.status}`);
      
      if (response.ok) {
        const certificates = await response.json();
        console.log('✅ Successfully fetched filtered certificates');
        console.log(`   Found ${certificates.length} favorite certificate(s)`);
      } else {
        console.log('❌ Failed to fetch filtered certificates');
      }
    } catch (error) {
      console.log('❌ Fetch failed:', error.message);
    }

    // Test 5: Test POST /api/certificates (create new certificate)
    console.log('\n5️⃣ Testing POST /api/certificates (create new)...');
    
    try {
      const mockSession = JSON.stringify({ user: { id: testUserId } });
      const newCertData = {
        name: 'Test Medical Certificate',
        issuingAuthority: 'Medical Institute',
        certificateNumber: 'MED-TEST-456',
        dateIssued: new Date().toISOString(),
        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
        isFavorite: false
      };
      
      const response = await fetch(`${baseUrl}/api/certificates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': `session=${mockSession}`
        },
        body: JSON.stringify(newCertData)
      });
      
      console.log(`   Status: ${response.status}`);
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ Successfully created certificate');
        console.log(`   New certificate ID: ${result.id}`);
      } else {
        const error = await response.text();
        console.log('❌ Failed to create certificate:', error);
      }
    } catch (error) {
      console.log('❌ Create request failed:', error.message);
    }

    // Cleanup: Remove test data
    console.log('\n🧹 Cleaning up test data...');
    await sql`DELETE FROM "Certificate" WHERE "userId" = ${testUserId};`;
    await sql`DELETE FROM "User" WHERE id = ${testUserId};`;
    console.log('✅ Test data cleaned up');

    console.log('\n🎉 API endpoint testing completed!');
    console.log('\nNext steps:');
    console.log('1. Test the frontend integration');
    console.log('2. Verify authentication flow');
    console.log('3. Test search, filtering, and sorting');

  } catch (error) {
    console.error('\n❌ API testing failed:', error);
  }
}

testApiEndpoints();

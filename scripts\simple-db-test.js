// Simple database connection test
const fs = require('fs');
const path = require('path');

// Load environment variables manually
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local');
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim().replace(/^"(.*)"$/, '$1');
        process.env[key.trim()] = value;
      }
    });
    
    console.log('✅ Environment variables loaded');
  } catch (error) {
    console.error('❌ Failed to load .env.local:', error.message);
    process.exit(1);
  }
}

loadEnvFile();

async function testConnection() {
  try {
    console.log('🔍 Available connection strings:');
    console.log('DATABASE_URL:', process.env.DATABASE_URL ? '✅ Set' : '❌ Missing');
    console.log('POSTGRES_URL:', process.env.POSTGRES_URL ? '✅ Set' : '❌ Missing');
    console.log('DATABASE_URL_UNPOOLED:', process.env.DATABASE_URL_UNPOOLED ? '✅ Set' : '❌ Missing');
    
    // Try to import and test the neon driver
    const { neon } = require("@neondatabase/serverless");
    
    // Use the unpooled connection for better compatibility
    const connectionString = process.env.DATABASE_URL_UNPOOLED || process.env.DATABASE_URL;
    console.log('\n🔗 Testing connection with:', connectionString ? 'Connection string found' : 'No connection string');
    
    if (!connectionString) {
      throw new Error('No database connection string available');
    }
    
    const sql = neon(connectionString);
    
    // Test basic connection
    console.log('\n🧪 Testing basic query...');
    const result = await sql`SELECT NOW() as current_time, version() as db_version`;
    console.log('✅ Database connection successful!');
    console.log('Current time:', result[0].current_time);
    console.log('Database version:', result[0].db_version.substring(0, 50) + '...');
    
    // Check if tables exist
    console.log('\n📋 Checking database schema...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;
    `;
    
    console.log('Available tables:');
    tables.forEach(table => {
      console.log(`  - ${table.table_name}`);
    });
    
    // Check Certificate table specifically
    const certificateTableExists = tables.some(t => t.table_name === 'Certificate');
    if (certificateTableExists) {
      console.log('\n✅ Certificate table found');
      
      // Check Certificate table schema
      const columns = await sql`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'Certificate' 
        ORDER BY ordinal_position;
      `;
      
      console.log('Certificate table columns:');
      columns.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
      });
      
      // Check if isFavorite column exists
      const hasFavoriteColumn = columns.some(col => col.column_name === 'isFavorite');
      if (!hasFavoriteColumn) {
        console.log('\n⚠️  isFavorite column missing - adding it now...');
        await sql`
          ALTER TABLE "Certificate" 
          ADD COLUMN "isFavorite" boolean DEFAULT false NOT NULL;
        `;
        console.log('✅ isFavorite column added successfully');
      } else {
        console.log('\n✅ isFavorite column already exists');
      }
      
    } else {
      console.log('\n❌ Certificate table not found');
      console.log('Available tables:', tables.map(t => t.table_name).join(', '));
    }
    
    console.log('\n🎉 Database test completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Database test failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

testConnection();

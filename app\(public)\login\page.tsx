"use client";

import type React from "react";

import Link from "next/link";
import { Anchor } from "lucide-react";
import { useState } from "react";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { logger } from "@/lib/logger";

export default function LoginPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    const endTimer = logger.authStart("user-login", email);
    logger.info("auth", "Login attempt started", {
      email: logger.isLoggingEnabled()
        ? email.substring(0, 3) + "***"
        : undefined,
    });

    try {
      // Use our custom login API endpoint
      const apiEndTimer = logger.apiRequest("POST", "/api/login");

      const response = await fetch("/api/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          password,
        }),
      });

      apiEndTimer();
      logger.apiResponse("POST", "/api/login", response.status);

      const data = await response.json();

      if (response.ok) {
        logger.authSuccess("user-login", data.user?.id || "unknown-user");
        logger.info("auth", "Login successful, redirecting to dashboard", {
          userId: data.user?.id,
        });

        // Add small delay to ensure session is properly set
        setTimeout(() => {
          logger.navigationStart("/login", "/dashboard", data.user?.id);
          router.push("/dashboard");
        }, 100);
      } else {
        logger.authFailure(
          "user-login",
          data.error || "Invalid credentials",
          email
        );
        setError(data.error || "Invalid email or password");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      logger.error("auth", "Login request failed", {
        error: errorMessage,
        email: email.substring(0, 3) + "***",
      });
      setError("Something went wrong. Please try again.");
    } finally {
      setIsLoading(false);
      endTimer();
    }
  };

  return (
    <div className="container flex items-center justify-center min-h-[calc(100vh-16rem)] py-12">
      <Card className="mx-auto max-w-sm">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-2">
            <Anchor className="h-10 w-10 text-primary" />
          </div>
          <CardTitle className="text-2xl text-center">
            Login to Sealog
          </CardTitle>
          <CardDescription className="text-center">
            Enter your email and password to access your account
          </CardDescription>
          <CardDescription className="text-center text-sm text-green-600">
            Demo account: <EMAIL> / demo123
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password">Password</Label>
                <Link href="#" className="text-sm text-primary hover:underline">
                  Forgot Password?
                </Link>
              </div>
              <Input
                id="password"
                type="password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <Button className="w-full" type="submit" disabled={isLoading}>
              {isLoading ? "Logging in..." : "Login to Sealog"}
            </Button>
            <div className="text-center text-sm">
              New to Sealog?{" "}
              <Link href="/signup" className="text-primary hover:underline">
                Sign Up
              </Link>
            </div>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}

/**
 * Migration script to add file metadata columns to the Certificate table
 * Run this script to add the new columns for file metadata support
 */

const { neon } = require("@neondatabase/serverless");
const fs = require("fs");
const path = require("path");

// Load environment variables from .env.local
function loadEnvFile() {
  const envPath = path.join(__dirname, "..", ".env.local");
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, "utf8");
    envContent.split("\n").forEach((line) => {
      const [key, ...valueParts] = line.split("=");
      if (key && valueParts.length > 0) {
        const value = valueParts.join("=").replace(/^["']|["']$/g, "");
        process.env[key] = value;
      }
    });
  }
}

// Load environment variables
loadEnvFile();

async function runMigration() {
  console.log(
    "🔄 Starting migration: Add file metadata columns to Certificate table"
  );

  try {
    const sql = neon(
      process.env.DATABASE_URL_UNPOOLED || process.env.DATABASE_URL
    );

    // Add the new columns
    console.log("📝 Adding documentName column...");
    await sql`
      ALTER TABLE "Certificate"
      ADD COLUMN IF NOT EXISTS "documentName" TEXT;
    `;

    console.log("📝 Adding documentSize column...");
    await sql`
      ALTER TABLE "Certificate"
      ADD COLUMN IF NOT EXISTS "documentSize" TEXT;
    `;

    console.log("📝 Adding documentType column...");
    await sql`
      ALTER TABLE "Certificate"
      ADD COLUMN IF NOT EXISTS "documentType" TEXT;
    `;

    // Verify the columns were added
    console.log("🔍 Verifying migration...");
    const result = await sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'Certificate'
      AND column_name IN ('documentName', 'documentSize', 'documentType')
      ORDER BY column_name;
    `;

    console.log("✅ Migration completed successfully!");
    console.log("📊 New columns added:");
    result.forEach((col) => {
      console.log(
        `   - ${col.column_name} (${col.data_type}, nullable: ${col.is_nullable})`
      );
    });

    // Check if there are any existing certificates with documents
    const existingCerts = await sql`
      SELECT COUNT(*) as count
      FROM "Certificate"
      WHERE "documentUrl" IS NOT NULL;
    `;

    if (existingCerts[0].count > 0) {
      console.log(
        `\n⚠️  Found ${existingCerts[0].count} existing certificates with documents.`
      );
      console.log(
        "   These certificates will have NULL values for the new metadata fields."
      );
      console.log("   This is expected and won't affect functionality.");
    }
  } catch (error) {
    console.error("❌ Migration failed:", error);
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  runMigration()
    .then(() => {
      console.log("\n🎉 Migration completed successfully!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n💥 Migration failed:", error);
      process.exit(1);
    });
}

module.exports = { runMigration };

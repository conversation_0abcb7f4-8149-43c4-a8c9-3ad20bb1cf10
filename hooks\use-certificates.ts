import { useState, useEffect, useCallback, useMemo } from 'react'

// Certificate type definition (matches database schema)
export type Certificate = {
  id: string
  name: string
  issuingAuthority: string
  certificateNumber: string
  dateIssued: Date
  expiryDate: Date | null
  documentUrl?: string | null
  notes?: string | null
  isFavorite: boolean
  createdAt: Date
  updatedAt: Date
  userId: string
  // Computed fields
  status: "active" | "expired" | "expiring-soon"
}

export type FilterType = "all" | "favorites" | "expiring-soon" | "expired"
export type SortField = "name" | "dateIssued" | "expiryDate"
export type SortOrder = "asc" | "desc"

interface UseCertificatesOptions {
  initialLoad?: boolean
}

interface CertificatesState {
  certificates: Certificate[]
  isLoading: boolean
  error: string | null
  lastFetch: Date | null
}

interface CertificatesActions {
  refetch: () => Promise<void>
  addCertificate: (certificate: Omit<Certificate, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) => Promise<void>
  updateCertificate: (id: string, updates: Partial<Certificate>) => Promise<void>
  deleteCertificate: (id: string) => Promise<void>
  toggleFavorite: (id: string) => Promise<void>
}

interface FilteredCertificatesResult {
  filteredCertificates: Certificate[]
  counts: {
    all: number
    favorites: number
    expiringSoon: number
    expired: number
  }
}

// Helper function to compute certificate status
function computeCertificateStatus(expiryDate: Date | null): "active" | "expired" | "expiring-soon" {
  if (!expiryDate) return "active"

  const now = new Date()
  const thirtyDaysFromNow = new Date()
  thirtyDaysFromNow.setDate(now.getDate() + 30)

  if (expiryDate < now) return "expired"
  if (expiryDate <= thirtyDaysFromNow) return "expiring-soon"
  return "active"
}

// Custom hook for managing certificates data
export function useCertificates(options: UseCertificatesOptions = {}) {
  const { initialLoad = true } = options

  const [state, setState] = useState<CertificatesState>({
    certificates: [],
    isLoading: initialLoad,
    error: null,
    lastFetch: null,
  })

  // Fetch certificates from API
  const fetchCertificates = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      const response = await fetch('/api/certificates', {
        credentials: 'include',
      })

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Unauthorized - please log in again')
        }
        throw new Error('Failed to fetch certificates')
      }

      const certificatesData = await response.json()

      // Transform API response to match our Certificate type
      const transformedCertificates: Certificate[] = certificatesData.map((cert: any) => ({
        ...cert,
        dateIssued: new Date(cert.dateIssued),
        expiryDate: cert.expiryDate ? new Date(cert.expiryDate) : null,
        createdAt: new Date(cert.createdAt),
        updatedAt: new Date(cert.updatedAt),
        status: computeCertificateStatus(cert.expiryDate ? new Date(cert.expiryDate) : null),
      }))

      setState(prev => ({
        ...prev,
        certificates: transformedCertificates,
        isLoading: false,
        lastFetch: new Date(),
      }))
    } catch (error) {
      console.error('Error fetching certificates:', error)
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to fetch certificates',
        isLoading: false,
      }))
    }
  }, [])

  // Add new certificate
  const addCertificate = useCallback(async (certificateData: Omit<Certificate, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) => {
    try {
      const response = await fetch('/api/certificates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          ...certificateData,
          dateIssued: certificateData.dateIssued.toISOString(),
          expiryDate: certificateData.expiryDate?.toISOString() || null,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to create certificate')
      }

      // Refetch to get the latest data
      await fetchCertificates()
    } catch (error) {
      console.error('Error adding certificate:', error)
      throw error
    }
  }, [fetchCertificates])

  // Update certificate
  const updateCertificate = useCallback(async (id: string, updates: Partial<Certificate>) => {
    try {
      const response = await fetch(`/api/certificates/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          ...updates,
          dateIssued: updates.dateIssued?.toISOString(),
          expiryDate: updates.expiryDate?.toISOString() || null,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to update certificate')
      }

      // Optimistically update the local state
      setState(prev => ({
        ...prev,
        certificates: prev.certificates.map(cert =>
          cert.id === id
            ? {
              ...cert,
              ...updates,
              status: updates.expiryDate !== undefined
                ? computeCertificateStatus(updates.expiryDate)
                : cert.status,
              updatedAt: new Date(),
            }
            : cert
        ),
      }))
    } catch (error) {
      console.error('Error updating certificate:', error)
      throw error
    }
  }, [])

  // Delete certificate
  const deleteCertificate = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/certificates/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error('Failed to delete certificate')
      }

      // Optimistically remove from local state
      setState(prev => ({
        ...prev,
        certificates: prev.certificates.filter(cert => cert.id !== id),
      }))
    } catch (error) {
      console.error('Error deleting certificate:', error)
      throw error
    }
  }, [])

  // Toggle favorite status
  const toggleFavorite = useCallback(async (id: string) => {
    let originalFavoriteStatus: boolean
    let currentCert: Certificate | undefined

    // Get current state and perform optimistic update
    setState(prev => {
      currentCert = prev.certificates.find(cert => cert.id === id)
      if (!currentCert) {
        throw new Error('Certificate not found')
      }

      originalFavoriteStatus = currentCert.isFavorite
      const newFavoriteStatus = !originalFavoriteStatus

      // Optimistically update the UI immediately
      return {
        ...prev,
        certificates: prev.certificates.map(cert =>
          cert.id === id
            ? { ...cert, isFavorite: newFavoriteStatus }
            : cert
        ),
      }
    })

    try {
      const response = await fetch(`/api/certificates/${id}/favorite`, {
        method: 'POST',
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error('Failed to toggle favorite')
      }

      const result = await response.json()

      // Update with the server response (should match our optimistic update)
      setState(prev => ({
        ...prev,
        certificates: prev.certificates.map(cert =>
          cert.id === id
            ? { ...cert, isFavorite: result.isFavorite }
            : cert
        ),
      }))
    } catch (error) {
      console.error('Error toggling favorite:', error)

      // Revert the optimistic update on error
      setState(prev => ({
        ...prev,
        certificates: prev.certificates.map(cert =>
          cert.id === id
            ? { ...cert, isFavorite: originalFavoriteStatus }
            : cert
        ),
      }))

      throw error
    }
  }, [])

  // Initial fetch
  useEffect(() => {
    if (initialLoad) {
      fetchCertificates()
    }
  }, [fetchCertificates, initialLoad])

  const actions: CertificatesActions = {
    refetch: fetchCertificates,
    addCertificate,
    updateCertificate,
    deleteCertificate,
    toggleFavorite,
  }

  return {
    ...state,
    ...actions,
  }
}

// Hook for client-side filtering and sorting
export function useFilteredCertificates(
  certificates: Certificate[],
  searchQuery: string = '',
  filter: FilterType = 'all',
  sortBy: SortField = 'expiryDate',
  sortOrder: SortOrder = 'asc'
): FilteredCertificatesResult {
  return useMemo(() => {
    let filtered = [...certificates]

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(cert =>
        cert.name.toLowerCase().includes(query) ||
        cert.certificateNumber.toLowerCase().includes(query) ||
        cert.issuingAuthority.toLowerCase().includes(query)
      )
    }

    // Apply status filter
    switch (filter) {
      case 'favorites':
        filtered = filtered.filter(cert => cert.isFavorite)
        break
      case 'expiring-soon':
        filtered = filtered.filter(cert => cert.status === 'expiring-soon')
        break
      case 'expired':
        filtered = filtered.filter(cert => cert.status === 'expired')
        break
      // 'all' case - no additional filtering needed
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
          break
        case 'dateIssued':
          aValue = a.dateIssued.getTime()
          bValue = b.dateIssued.getTime()
          break
        case 'expiryDate':
          // Handle null expiry dates (put them at the end)
          aValue = a.expiryDate?.getTime() ?? Infinity
          bValue = b.expiryDate?.getTime() ?? Infinity
          break
        default:
          return 0
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1
      return 0
    })

    // Calculate counts
    const counts = {
      all: certificates.length,
      favorites: certificates.filter(c => c.isFavorite).length,
      expiringSoon: certificates.filter(c => c.status === 'expiring-soon').length,
      expired: certificates.filter(c => c.status === 'expired').length,
    }

    return {
      filteredCertificates: filtered,
      counts,
    }
  }, [certificates, searchQuery, filter, sortBy, sortOrder])
}

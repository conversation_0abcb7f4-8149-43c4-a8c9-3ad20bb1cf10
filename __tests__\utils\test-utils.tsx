import React from "react";
import { render, RenderOptions } from "@testing-library/react";

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => "/certificates",
}));

// Mock fetch globally
global.fetch = jest.fn();

// Custom render function with providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return <div>{children}</div>;
};

const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, "wrapper">
) => render(ui, { wrapper: AllTheProviders, ...options });

// Test data factories
export const createMockCertificate = (overrides = {}) => ({
  id: "cert-1",
  name: "Test Certificate",
  issuingAuthority: "Test Authority",
  certificateNumber: "TEST-001",
  dateIssued: new Date("2023-01-01"),
  expiryDate: new Date("2024-01-01"),
  documentUrl: "https://example.com/cert.pdf",
  notes: "Test notes",
  isFavorite: false,
  createdAt: new Date("2023-01-01"),
  updatedAt: new Date("2023-01-01"),
  userId: "user-1",
  status: "active" as const,
  ...overrides,
});

export const createMockCertificates = (count = 5) => {
  return Array.from({ length: count }, (_, index) =>
    createMockCertificate({
      id: `cert-${index + 1}`,
      name: `Certificate ${index + 1}`,
      certificateNumber: `TEST-${String(index + 1).padStart(3, "0")}`,
      status:
        index % 3 === 0
          ? "expired"
          : index % 3 === 1
          ? "expiring-soon"
          : "active",
      isFavorite: index % 2 === 0,
    })
  );
};

// Mock file for testing file uploads
export const createMockFile = (
  name = "test.pdf",
  type = "application/pdf",
  size = 1024 * 1024 // 1MB
) => {
  const file = new File(["test content"], name, { type });
  Object.defineProperty(file, "size", { value: size });
  return file;
};

// Mock fetch responses
export const mockFetchSuccess = (data: any) => {
  (global.fetch as jest.Mock).mockResolvedValueOnce({
    ok: true,
    status: 200,
    json: async () => data,
  });
};

export const mockFetchError = (status = 500, message = "Server Error") => {
  (global.fetch as jest.Mock).mockResolvedValueOnce({
    ok: false,
    status,
    text: async () => message,
    json: async () => ({ error: message }),
  });
};

// Utility to wait for async operations
export const waitForAsync = () =>
  new Promise((resolve) => setTimeout(resolve, 0));

// Mock drag and drop events
export const createMockDragEvent = (files: File[] = []) => {
  return {
    preventDefault: jest.fn(),
    stopPropagation: jest.fn(),
    dataTransfer: {
      files,
    },
    currentTarget: {
      getBoundingClientRect: () => ({
        left: 0,
        top: 0,
        right: 100,
        bottom: 100,
      }),
    },
    clientX: 50,
    clientY: 50,
  } as any;
};

// Mock keyboard events
export const createMockKeyboardEvent = (key: string) => {
  return {
    key,
    preventDefault: jest.fn(),
  } as any;
};

// Cleanup function for tests
export const cleanup = () => {
  jest.clearAllMocks();
  (global.fetch as jest.Mock).mockClear();
};

// Re-export everything from testing-library
export * from "@testing-library/react";
export { customRender as render };

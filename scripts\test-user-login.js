// Test login for the actual user account
const fs = require('fs');
const path = require('path');

// Load environment variables
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local');
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim().replace(/^"(.*)"$/, '$1');
        process.env[key.trim()] = value;
      }
    });
  } catch (error) {
    console.error('Failed to load .env.local:', error.message);
  }
}

loadEnvFile();

async function testUserLogin() {
  console.log('🧪 Testing User Login for Actual Account');
  console.log('========================================\n');

  const baseUrl = 'http://localhost:3000';
  const userEmail = '<EMAIL>';
  const userPassword = 'CKCready2';

  try {
    console.log('1️⃣ Checking if user exists in database...');
    
    const { neon } = require("@neondatabase/serverless");
    const sql = neon(process.env.DATABASE_URL_UNPOOLED || process.env.DATABASE_URL);
    
    const users = await sql`
      SELECT id, email, name, "createdAt" FROM "User" 
      WHERE email = ${userEmail} 
      LIMIT 1;
    `;
    
    if (users.length === 0) {
      console.log('❌ User not found in database!');
      console.log('   This means the signup process may have failed.');
      console.log('   Let me check all users in the database...');
      
      const allUsers = await sql`
        SELECT id, email, name, "createdAt" FROM "User" 
        ORDER BY "createdAt" DESC 
        LIMIT 10;
      `;
      
      console.log(`\n📋 Found ${allUsers.length} users in database:`);
      allUsers.forEach(user => {
        console.log(`   - ${user.email} (${user.name}) - Created: ${user.createdAt.toISOString().split('T')[0]}`);
      });
      
      return;
    }
    
    const user = users[0];
    console.log('✅ User found in database:');
    console.log(`   ID: ${user.id}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Name: ${user.name}`);
    console.log(`   Created: ${user.createdAt.toISOString()}`);

    console.log('\n2️⃣ Testing login API...');
    
    const loginResponse = await fetch(`${baseUrl}/api/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: userEmail,
        password: userPassword,
      }),
    });

    console.log(`Login response status: ${loginResponse.status}`);
    
    if (!loginResponse.ok) {
      const error = await loginResponse.text();
      console.log('❌ Login failed:', error);
      
      // Test password verification manually
      console.log('\n3️⃣ Testing password verification...');
      const { compare } = require('bcrypt');
      
      const userWithPassword = await sql`
        SELECT password FROM "User" WHERE email = ${userEmail} LIMIT 1;
      `;
      
      if (userWithPassword.length > 0) {
        const isPasswordValid = await compare(userPassword, userWithPassword[0].password);
        console.log(`Password verification: ${isPasswordValid ? '✅ Valid' : '❌ Invalid'}`);
        
        if (!isPasswordValid) {
          console.log('⚠️  The password in the database does not match the provided password.');
          console.log('   This could mean:');
          console.log('   1. The signup process had an issue');
          console.log('   2. The password was entered incorrectly during signup');
          console.log('   3. There was an error in the password hashing');
        }
      }
      
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful!');
    console.log(`   User: ${loginData.user.name} (${loginData.user.email})`);

    // Extract session cookie
    const setCookieHeader = loginResponse.headers.get('set-cookie');
    if (!setCookieHeader) {
      console.log('⚠️  No session cookie received');
      return;
    }

    const sessionCookie = setCookieHeader.split(';')[0];
    console.log('✅ Session cookie received');

    console.log('\n4️⃣ Testing certificates API with user session...');

    const certsResponse = await fetch(`${baseUrl}/api/certificates`, {
      headers: {
        'Cookie': sessionCookie
      }
    });

    if (!certsResponse.ok) {
      const error = await certsResponse.text();
      console.log('❌ Failed to fetch certificates:', error);
      return;
    }

    const certificates = await certsResponse.json();
    console.log(`✅ Retrieved ${certificates.length} certificates for user`);
    
    if (certificates.length === 0) {
      console.log('ℹ️  No certificates found for this user (this is normal for a new account)');
      
      console.log('\n5️⃣ Creating a sample certificate for the user...');
      
      const sampleCert = {
        name: 'Welcome Certificate',
        issuingAuthority: 'Sealog System',
        certificateNumber: 'WELCOME-' + Date.now(),
        dateIssued: new Date().toISOString(),
        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
        isFavorite: true,
        notes: 'Welcome to Sealog! This is your first certificate.'
      };
      
      const createResponse = await fetch(`${baseUrl}/api/certificates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': sessionCookie
        },
        body: JSON.stringify(sampleCert)
      });
      
      if (createResponse.ok) {
        const createResult = await createResponse.json();
        console.log('✅ Sample certificate created successfully');
        console.log(`   Certificate ID: ${createResult.id}`);
      } else {
        const error = await createResponse.text();
        console.log('❌ Failed to create sample certificate:', error);
      }
    } else {
      console.log('Sample certificates:');
      certificates.slice(0, 3).forEach(cert => {
        const favorite = cert.isFavorite ? '⭐' : '☆';
        const expiry = cert.expiryDate ? new Date(cert.expiryDate).toISOString().split('T')[0] : 'No Expiry';
        console.log(`   ${favorite} ${cert.name} (Expires: ${expiry})`);
      });
    }

    console.log('\n🎉 User login test completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ User exists in database');
    console.log('✅ Login API works');
    console.log('✅ Session management works');
    console.log('✅ Certificates API works');
    
    console.log('\n🚀 Ready to test in browser!');
    console.log('1. Navigate to: http://localhost:3000/login');
    console.log(`2. Login with: ${userEmail} / ${userPassword}`);
    console.log('3. You should be redirected to: http://localhost:3000/certificates');

  } catch (error) {
    console.error('\n❌ User login test failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 Tip: Make sure your development server is running:');
      console.log('   npm run dev');
    }
  }
}

testUserLogin();

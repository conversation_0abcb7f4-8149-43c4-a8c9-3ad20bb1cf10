import Link from "next/link";
import {
  Calendar,
  Download,
  Edit,
  FileText,
  Share2,
  Trash2,
  User,
  Upload,
} from "lucide-react";
import { notFound } from "next/navigation";
import { cookies } from "next/headers";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CertificateStatusBadge } from "@/components/certificate-status-badge";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { getCertificateById } from "@/lib/db";

// Helper function to get user from session cookie
async function getUserFromSession() {
  try {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get("session");
    if (!sessionCookie) {
      return null;
    }

    const session = JSON.parse(sessionCookie.value);
    return session.user;
  } catch (error) {
    console.error("Error parsing session:", error);
    return null;
  }
}

export default async function CertificateDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  // Await the params object as required by Next.js 15
  const { id } = await params;

  // Get user from session
  const user = await getUserFromSession();
  if (!user?.id) {
    notFound(); // Redirect to login or show unauthorized
  }

  // Fetch the certificate directly from the database
  let certificate;
  try {
    certificate = await getCertificateById(id, user.id);

    if (!certificate) {
      notFound();
    }
  } catch (error) {
    console.error("Error fetching certificate:", error);
    notFound();
  }

  const formatDate = (date: Date | null) => {
    if (!date) return "No Expiry";
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getDaysRemaining = (expiryDate: Date | null) => {
    if (!expiryDate) return null;
    const today = new Date();
    const diffTime = new Date(expiryDate).getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getDaysRemainingText = (expiryDate: Date | null) => {
    if (!expiryDate) return "No Expiry";
    const days = getDaysRemaining(expiryDate);
    if (!days) return "No Expiry";
    if (days < 0) return `Expired ${Math.abs(days)} days ago`;
    if (days === 0) return "Expires today";
    return `${days} days remaining`;
  };

  const getDaysRemainingClass = (expiryDate: Date | null) => {
    if (!expiryDate) return "text-gray-500";
    const days = getDaysRemaining(expiryDate);
    if (!days) return "text-gray-500";
    if (days < 0) return "text-red-500";
    if (days <= 30) return "text-orange-500";
    if (days <= 90) return "text-yellow-500";
    return "text-green-500";
  };

  return (
    <div className="space-y-6 p-6 md:p-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <div className="flex items-center gap-2">
            <h1 className="text-3xl font-bold tracking-tight">
              {certificate.name}
            </h1>
            <CertificateStatusBadge expiryDate={certificate.expiryDate} />
          </div>
          <p className="text-muted-foreground">
            Certificate #{certificate.certificateNumber}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/certificates">Back to Certificates</Link>
          </Button>
          <Button asChild>
            <Link href={`/certificates/${certificate.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" /> Edit Certificate
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Certificate Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Certificate Name
                  </h3>
                  <p className="text-base">{certificate.name}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Certificate Number
                  </h3>
                  <p className="text-base">{certificate.certificateNumber}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Issuing Authority
                  </h3>
                  <p className="text-base">{certificate.issuingAuthority}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Date Issued
                  </h3>
                  <p className="text-base">
                    {formatDate(certificate.dateIssued)}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Expiry Date
                  </h3>
                  <p className="text-base">
                    {formatDate(certificate.expiryDate)}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Status
                  </h3>
                  <p
                    className={`text-base font-medium ${getDaysRemainingClass(
                      certificate.expiryDate
                    )}`}
                  >
                    {getDaysRemainingText(certificate.expiryDate)}
                  </p>
                </div>
              </div>

              {certificate.notes && (
                <>
                  <Separator />
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-2">
                      Notes
                    </h3>
                    <p className="text-sm">{certificate.notes}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-xl">Certificate Document</CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col items-center justify-center p-8 border-2 border-dashed rounded-lg">
              {certificate.documentUrl ? (
                <>
                  <FileText className="h-16 w-16 text-primary mb-4" />
                  <p className="text-foreground font-medium mb-2">
                    Document Available
                  </p>
                  <div className="text-muted-foreground text-sm mb-4 space-y-1">
                    <p>Certificate document has been uploaded</p>
                    {certificate.documentName && (
                      <p>
                        <strong>File:</strong> {certificate.documentName}
                      </p>
                    )}
                    {certificate.documentSize && (
                      <p>
                        <strong>Size:</strong> {certificate.documentSize}
                      </p>
                    )}
                    {certificate.documentType && (
                      <p>
                        <strong>Type:</strong> {certificate.documentType}
                      </p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Button asChild>
                      <a
                        href={certificate.documentUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Download className="mr-2 h-4 w-4" /> Download
                      </a>
                    </Button>
                    <Button variant="outline">
                      <Upload className="mr-2 h-4 w-4" /> Replace
                    </Button>
                  </div>
                </>
              ) : (
                <>
                  <FileText className="h-16 w-16 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">No document uploaded</p>
                  <Button variant="outline" className="mt-4">
                    <Upload className="mr-2 h-4 w-4" /> Upload Document
                  </Button>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Certificate Timeline</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="mt-0.5">
                  <Badge
                    variant="outline"
                    className="rounded-full h-6 w-6 flex items-center justify-center p-0"
                  >
                    <Calendar className="h-3 w-3" />
                  </Badge>
                </div>
                <div>
                  <p className="font-medium">Certificate Issued</p>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(certificate.dateIssued)}
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="mt-0.5">
                  <Badge
                    variant="outline"
                    className="rounded-full h-6 w-6 flex items-center justify-center p-0"
                  >
                    <User className="h-3 w-3" />
                  </Badge>
                </div>
                <div>
                  <p className="font-medium">Added to Sealog</p>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(certificate.createdAt)}
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="mt-0.5">
                  <Badge
                    variant="outline"
                    className={`rounded-full h-6 w-6 flex items-center justify-center p-0 ${
                      getDaysRemaining(certificate.expiryDate)! < 0
                        ? "border-red-500 text-red-500"
                        : "border-yellow-500 text-yellow-500"
                    }`}
                  >
                    <Calendar className="h-3 w-3" />
                  </Badge>
                </div>
                <div>
                  <p className="font-medium">Certificate Expiry</p>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(certificate.expiryDate)}
                  </p>
                  <p
                    className={`text-sm font-medium ${getDaysRemainingClass(
                      certificate.expiryDate
                    )}`}
                  >
                    {getDaysRemainingText(certificate.expiryDate)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <Download className="mr-2 h-4 w-4" /> Download Certificate
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Share2 className="mr-2 h-4 w-4" /> Share Certificate
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start text-red-500 hover:text-red-600 hover:bg-red-50"
              >
                <Trash2 className="mr-2 h-4 w-4" /> Delete Certificate
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

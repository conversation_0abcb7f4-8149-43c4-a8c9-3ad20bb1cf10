import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { getCertificateFiles, getCertificateById } from "@/lib/db"

// Helper function to get user from session cookie
async function getUserFromSession() {
  try {
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get("session")
    if (!sessionCookie) {
      return null
    }

    const session = JSON.parse(sessionCookie.value)
    return session.user
  } catch (error) {
    console.error("Error parsing session:", error)
    return null
  }
}

// GET /api/certificates/[id]/files - Get all files for a certificate
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = await params
    
    // Verify certificate exists and belongs to user
    const certificate = await getCertificateById(id, user.id)
    if (!certificate) {
      return NextResponse.json({ error: "Certificate not found" }, { status: 404 })
    }

    // Get all files for this certificate
    const files = await getCertificateFiles(id, user.id)
    
    // If no files in new table, check legacy fields
    if (files.length === 0 && certificate.documentUrl) {
      // Return legacy file as array for consistency
      const legacyFile = {
        id: `legacy-${id}`,
        certificateId: id,
        fileName: certificate.documentName || 'certificate.pdf',
        fileUrl: certificate.documentUrl,
        fileSize: certificate.documentSize ? parseInt(certificate.documentSize.replace(/[^\d]/g, '')) * 1024 : 0,
        fileType: certificate.documentType || 'application/pdf',
        uploadthingKey: null,
        uploadOrder: 0,
        createdAt: certificate.createdAt,
      }
      return NextResponse.json([legacyFile])
    }

    return NextResponse.json(files)
  } catch (error) {
    console.error("Error fetching certificate files:", error)
    return NextResponse.json(
      { error: "Failed to fetch certificate files" },
      { status: 500 }
    )
  }
}

import React from 'react'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import NewCertificatePage from '@/app/(app)/certificates/new/page'
import {
  render,
  createMockFile,
  createMockDragEvent,
  mockFetchSuccess,
  mockFetchError,
  cleanup,
} from '../utils/test-utils'

// Mock Uploadthing
jest.mock('@/lib/uploadthing', () => ({
  UploadDropzone: ({ onClientUploadComplete, onUploadError }: any) => (
    <div data-testid="upload-dropzone">
      <button
        onClick={() => {
          // Simulate successful upload
          onClientUploadComplete([
            {
              url: 'https://uploadthing.com/test-file.pdf',
              name: 'test-file.pdf',
            },
          ])
        }}
      >
        Upload File
      </button>
      <button
        onClick={() => {
          // Simulate upload error
          onUploadError(new Error('Upload failed'))
        }}
      >
        Simulate Error
      </button>
    </div>
  ),
}))

// Mock date picker
jest.mock('@/components/ui/calendar', () => ({
  Calendar: ({ onSelect }: any) => (
    <div data-testid="calendar">
      <button onClick={() => onSelect(new Date('2024-01-01'))}>
        Select Date
      </button>
    </div>
  ),
}))

describe('File Upload Integration', () => {
  beforeEach(() => {
    cleanup()
  })

  describe('Uploadthing Integration', () => {
    it('uploads file successfully and saves URL', async () => {
      const user = userEvent.setup()
      mockFetchSuccess({ id: 'new-cert-id' })

      render(<NewCertificatePage />)

      // Fill out required form fields
      await user.type(screen.getByLabelText(/certificate name/i), 'Test Certificate')
      await user.type(screen.getByLabelText(/issuing authority/i), 'Test Authority')
      await user.type(screen.getByLabelText(/certificate number/i), 'TEST-001')

      // Set issued date
      const issuedDateButton = screen.getByText(/select date issued/i)
      await user.click(issuedDateButton)
      const selectDateButton = screen.getByText('Select Date')
      await user.click(selectDateButton)

      // Upload file
      const uploadButton = screen.getByText('Upload File')
      await user.click(uploadButton)

      // Verify file is uploaded and displayed
      await waitFor(() => {
        expect(screen.getByText('test-file.pdf')).toBeInTheDocument()
      })

      // Submit form
      const submitButton = screen.getByRole('button', { name: /create certificate/i })
      await user.click(submitButton)

      // Verify API call includes file URL
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/certificates', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: expect.stringContaining('https://uploadthing.com/test-file.pdf'),
        })
      })
    })

    it('handles upload errors gracefully', async () => {
      const user = userEvent.setup()
      render(<NewCertificatePage />)

      // Simulate upload error
      const errorButton = screen.getByText('Simulate Error')
      await user.click(errorButton)

      // Verify error is displayed
      await waitFor(() => {
        expect(screen.getByText(/upload failed/i)).toBeInTheDocument()
      })
    })

    it('allows file removal after upload', async () => {
      const user = userEvent.setup()
      render(<NewCertificatePage />)

      // Upload file
      const uploadButton = screen.getByText('Upload File')
      await user.click(uploadButton)

      // Verify file is displayed
      await waitFor(() => {
        expect(screen.getByText('test-file.pdf')).toBeInTheDocument()
      })

      // Remove file
      const removeButton = screen.getByRole('button', { name: /remove/i })
      await user.click(removeButton)

      // Verify file is removed and upload area is shown again
      expect(screen.queryByText('test-file.pdf')).not.toBeInTheDocument()
      expect(screen.getByTestId('upload-dropzone')).toBeInTheDocument()
    })
  })

  describe('Form Validation with File Upload', () => {
    it('submits form without file upload', async () => {
      const user = userEvent.setup()
      mockFetchSuccess({ id: 'new-cert-id' })

      render(<NewCertificatePage />)

      // Fill out required fields only
      await user.type(screen.getByLabelText(/certificate name/i), 'Test Certificate')
      await user.type(screen.getByLabelText(/issuing authority/i), 'Test Authority')
      await user.type(screen.getByLabelText(/certificate number/i), 'TEST-001')

      // Set issued date
      const issuedDateButton = screen.getByText(/select date issued/i)
      await user.click(issuedDateButton)
      const selectDateButton = screen.getByText('Select Date')
      await user.click(selectDateButton)

      // Submit without file
      const submitButton = screen.getByRole('button', { name: /create certificate/i })
      await user.click(submitButton)

      // Verify API call with null documentUrl
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/certificates', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: expect.stringContaining('"documentUrl":null'),
        })
      })
    })

    it('validates required fields before allowing submission', async () => {
      const user = userEvent.setup()
      render(<NewCertificatePage />)

      // Try to submit without required fields
      const submitButton = screen.getByRole('button', { name: /create certificate/i })
      await user.click(submitButton)

      // Should show validation error
      expect(screen.getByText(/please fill in all required fields/i)).toBeInTheDocument()
      expect(global.fetch).not.toHaveBeenCalled()
    })

    it('handles API errors during certificate creation', async () => {
      const user = userEvent.setup()
      mockFetchError(400, 'Validation failed')

      render(<NewCertificatePage />)

      // Fill out form
      await user.type(screen.getByLabelText(/certificate name/i), 'Test Certificate')
      await user.type(screen.getByLabelText(/issuing authority/i), 'Test Authority')
      await user.type(screen.getByLabelText(/certificate number/i), 'TEST-001')

      // Set issued date
      const issuedDateButton = screen.getByText(/select date issued/i)
      await user.click(issuedDateButton)
      const selectDateButton = screen.getByText('Select Date')
      await user.click(selectDateButton)

      // Submit form
      const submitButton = screen.getByRole('button', { name: /create certificate/i })
      await user.click(submitButton)

      // Should show API error
      await waitFor(() => {
        expect(screen.getByText(/validation failed/i)).toBeInTheDocument()
      })
    })
  })

  describe('File Upload Progress and States', () => {
    it('shows loading state during form submission', async () => {
      const user = userEvent.setup()
      
      // Mock slow API response
      let resolvePromise: (value: any) => void
      const slowPromise = new Promise(resolve => {
        resolvePromise = resolve
      })
      ;(global.fetch as jest.Mock).mockReturnValue(
        slowPromise.then(() => ({
          ok: true,
          json: async () => ({ id: 'new-cert-id' }),
        }))
      )

      render(<NewCertificatePage />)

      // Fill out form
      await user.type(screen.getByLabelText(/certificate name/i), 'Test Certificate')
      await user.type(screen.getByLabelText(/issuing authority/i), 'Test Authority')
      await user.type(screen.getByLabelText(/certificate number/i), 'TEST-001')

      // Set issued date
      const issuedDateButton = screen.getByText(/select date issued/i)
      await user.click(issuedDateButton)
      const selectDateButton = screen.getByText('Select Date')
      await user.click(selectDateButton)

      // Submit form
      const submitButton = screen.getByRole('button', { name: /create certificate/i })
      await user.click(submitButton)

      // Should show loading state
      expect(screen.getByText(/creating/i)).toBeInTheDocument()
      expect(submitButton).toBeDisabled()

      // Resolve the promise
      resolvePromise!({ ok: true, json: async () => ({ id: 'new-cert-id' }) })

      // Wait for completion
      await waitFor(() => {
        expect(screen.queryByText(/creating/i)).not.toBeInTheDocument()
      })
    })

    it('handles authentication errors during upload', async () => {
      const user = userEvent.setup()
      mockFetchError(401, 'Unauthorized')

      render(<NewCertificatePage />)

      // Fill out form
      await user.type(screen.getByLabelText(/certificate name/i), 'Test Certificate')
      await user.type(screen.getByLabelText(/issuing authority/i), 'Test Authority')
      await user.type(screen.getByLabelText(/certificate number/i), 'TEST-001')

      // Set issued date
      const issuedDateButton = screen.getByText(/select date issued/i)
      await user.click(issuedDateButton)
      const selectDateButton = screen.getByText('Select Date')
      await user.click(selectDateButton)

      // Submit form
      const submitButton = screen.getByRole('button', { name: /create certificate/i })
      await user.click(submitButton)

      // Should redirect to login (mocked router push)
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalled()
      })
    })
  })

  describe('Date Handling', () => {
    it('handles expiry date correctly', async () => {
      const user = userEvent.setup()
      mockFetchSuccess({ id: 'new-cert-id' })

      render(<NewCertificatePage />)

      // Fill out form
      await user.type(screen.getByLabelText(/certificate name/i), 'Test Certificate')
      await user.type(screen.getByLabelText(/issuing authority/i), 'Test Authority')
      await user.type(screen.getByLabelText(/certificate number/i), 'TEST-001')

      // Set issued date
      const issuedDateButton = screen.getByText(/select date issued/i)
      await user.click(issuedDateButton)
      let selectDateButton = screen.getByText('Select Date')
      await user.click(selectDateButton)

      // Set expiry date
      const expiryDateButton = screen.getByText(/select expiry date/i)
      await user.click(expiryDateButton)
      selectDateButton = screen.getByText('Select Date')
      await user.click(selectDateButton)

      // Submit form
      const submitButton = screen.getByRole('button', { name: /create certificate/i })
      await user.click(submitButton)

      // Verify dates are properly formatted in API call
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/certificates', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: expect.stringContaining('"dateIssued":"2024-01-01T00:00:00.000Z"'),
        })
      })
    })

    it('handles no expiry date option', async () => {
      const user = userEvent.setup()
      mockFetchSuccess({ id: 'new-cert-id' })

      render(<NewCertificatePage />)

      // Fill out form
      await user.type(screen.getByLabelText(/certificate name/i), 'Test Certificate')
      await user.type(screen.getByLabelText(/issuing authority/i), 'Test Authority')
      await user.type(screen.getByLabelText(/certificate number/i), 'TEST-001')

      // Set issued date
      const issuedDateButton = screen.getByText(/select date issued/i)
      await user.click(issuedDateButton)
      const selectDateButton = screen.getByText('Select Date')
      await user.click(selectDateButton)

      // Check no expiry option
      const noExpiryCheckbox = screen.getByLabelText(/no expiry date/i)
      await user.click(noExpiryCheckbox)

      // Submit form
      const submitButton = screen.getByRole('button', { name: /create certificate/i })
      await user.click(submitButton)

      // Verify expiry date is null
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/certificates', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: expect.stringContaining('"expiryDate":null'),
        })
      })
    })
  })

  describe('Mobile Responsiveness', () => {
    it('works correctly on mobile devices', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })

      const user = userEvent.setup()
      render(<NewCertificatePage />)

      // Should render all form elements
      expect(screen.getByLabelText(/certificate name/i)).toBeInTheDocument()
      expect(screen.getByTestId('upload-dropzone')).toBeInTheDocument()

      // Touch interactions should work
      const uploadButton = screen.getByText('Upload File')
      await user.click(uploadButton)

      await waitFor(() => {
        expect(screen.getByText('test-file.pdf')).toBeInTheDocument()
      })
    })
  })
})

import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Get the session cookie
  const session = request.cookies.get("session")

  // Define public routes that don't require authentication
  const publicRoutes = ["/", "/login", "/signup", "/auth/error"]
  const isPublicRoute = publicRoutes.some((route) => pathname === route || pathname.startsWith(route + "/"))

  // Define app routes that require authentication
  const isAppRoute =
    pathname.startsWith("/dashboard") ||
    pathname.startsWith("/certificates") ||
    pathname.startsWith("/profile") ||
    pathname.startsWith("/notifications") ||
    pathname.startsWith("/settings") ||
    pathname.startsWith("/help")

  // Redirect logic
  if (isAppRoute && !session) {
    // Redirect to login if trying to access protected route without session
    const url = new URL("/login", request.url)
    url.searchParams.set("callbackUrl", encodeURI(request.url))
    return NextResponse.redirect(url)
  }

  if ((pathname === "/login" || pathname === "/signup") && session) {
    // Redirect to dashboard if already logged in and trying to access login/signup
    return NextResponse.redirect(new URL("/dashboard", request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
}

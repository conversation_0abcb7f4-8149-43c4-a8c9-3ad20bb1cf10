import {
  Eye,
  Star,
  Download,
  Edit,
  ArrowUpDown,
  ChevronDown,
  ChevronUp,
  Trash2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useState } from "react";
import {
  downloadCertificateDocument,
  getCertificateViewUrl,
  hasDownloadableDocument,
  openFileInNewTab,
  isFileViewable,
  viewAllCertificateFiles,
  downloadCertificateFilesAsZip,
  getCertificateFiles,
} from "@/lib/download-utils";
import { DeleteConfirmationDialog } from "@/components/delete-confirmation-dialog";
import { useCertificates } from "@/contexts/certificates-context";
import { useToast } from "@/hooks/use-toast";

interface CertificateTableProps {
  certificates: Array<{
    id: string;
    name: string;
    issuingAuthority: string;
    certificateNumber: string;
    dateIssued: Date;
    expiryDate: Date | null;
    isFavorite?: boolean;
    status: "active" | "expired" | "expiring-soon";
    documentUrl?: string | null;
    documentName?: string | null;
    documentSize?: string | null;
    documentType?: string | null;
  }>;
  onView: (id: string) => void;
}

export function CertificateTable({
  certificates,
  onView,
}: CertificateTableProps) {
  const [downloadingIds, setDownloadingIds] = useState<Set<string>>(new Set());
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deletingCertificate, setDeletingCertificate] = useState<string | null>(
    null
  );
  const [isDeleting, setIsDeleting] = useState(false);
  const [togglingFavoriteIds, setTogglingFavoriteIds] = useState<Set<string>>(
    new Set()
  );

  const { toggleFavorite, deleteCertificate } = useCertificates();
  const { toast } = useToast();
  const handleDownload = async (certificateId: string) => {
    if (downloadingIds.has(certificateId)) return;

    setDownloadingIds((prev) => new Set(prev).add(certificateId));
    try {
      // Try to get all files for this certificate
      const files = await getCertificateFiles(certificateId);

      if (files.length > 1) {
        // Multiple files - download as ZIP
        await downloadCertificateFilesAsZip(certificateId);
      } else if (files.length === 1) {
        // Single file - use direct download
        await downloadCertificateDocument(certificateId);
      } else {
        // Fallback to legacy download
        await downloadCertificateDocument(certificateId);
      }
    } catch (error) {
      console.error("Download failed:", error);
      // You could add a toast notification here
    } finally {
      setDownloadingIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(certificateId);
        return newSet;
      });
    }
  };

  const handleViewFiles = async (
    certificateId: string,
    e: React.MouseEvent
  ) => {
    e.stopPropagation();
    const cert = certificates.find((c) => c.id === certificateId);
    if (!cert || !hasDownloadableDocument(cert)) return;

    try {
      // Try to get all files for this certificate
      const files = await getCertificateFiles(certificateId);

      if (files.length > 1) {
        // Multiple files - open all in new tabs
        await viewAllCertificateFiles(certificateId);
      } else if (files.length === 1) {
        // Single file - check if viewable
        const file = files[0];
        if (isFileViewable(file.fileName, file.fileType)) {
          openFileInNewTab(file.fileUrl);
        } else {
          // If not viewable, download instead
          await downloadCertificateDocument(certificateId);
        }
      } else {
        // Fallback to legacy view
        const viewData = await getCertificateViewUrl(certificateId);
        if (isFileViewable(viewData.fileName, viewData.fileType)) {
          openFileInNewTab(viewData.viewUrl);
        } else {
          // If not viewable, download instead
          await downloadCertificateDocument(certificateId);
        }
      }
    } catch (error) {
      console.error("View failed:", error);
    }
  };

  const handleToggleFavorite = async (
    certificateId: string,
    e: React.MouseEvent
  ) => {
    e.stopPropagation();
    if (togglingFavoriteIds.has(certificateId)) return; // Prevent multiple clicks

    const cert = certificates.find((c) => c.id === certificateId);
    if (!cert) return;

    const wasFavorite = cert.isFavorite;
    setTogglingFavoriteIds((prev) => new Set(prev).add(certificateId));

    try {
      await toggleFavorite(certificateId);
      // Show success toast
      toast({
        title: wasFavorite ? "Removed from favorites" : "Added to favorites",
        description: `${cert.name} has been ${
          wasFavorite ? "removed from" : "added to"
        } your favorites.`,
      });
    } catch (error) {
      console.error("Failed to toggle favorite:", error);
      toast({
        title: "Error",
        description: "Failed to update favorite status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setTogglingFavoriteIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(certificateId);
        return newSet;
      });
    }
  };

  const handleDeleteClick = (certificateId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setDeletingCertificate(certificateId);
    setShowDeleteDialog(true);
  };

  const handleDelete = async () => {
    if (!deletingCertificate) return;

    setIsDeleting(true);
    try {
      await deleteCertificate(deletingCertificate);
      setShowDeleteDialog(false);
      setDeletingCertificate(null);
    } catch (error) {
      console.error("Delete failed:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleRowClick = (certificateId: string) => {
    onView(certificateId);
  };

  const formatDate = (date: Date | null) => {
    if (!date) return "No Expiry";
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            Active
          </Badge>
        );
      case "expiring-soon":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            Expiring Soon
          </Badge>
        );
      case "expired":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            Expired
          </Badge>
        );
      default:
        return null;
    }
  };

  const getDaysUntilExpiry = (expiryDate: Date | null) => {
    if (!expiryDate) return null;
    const today = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <TooltipProvider>
      <div className="rounded-md border overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50">
              <TableHead className="font-semibold">Certificate</TableHead>
              <TableHead className="font-semibold hidden sm:table-cell">
                Issuing Authority
              </TableHead>
              <TableHead className="font-semibold hidden md:table-cell">
                Certificate Number
              </TableHead>
              <TableHead className="font-semibold hidden lg:table-cell">
                Issued
              </TableHead>
              <TableHead className="font-semibold">Expires</TableHead>
              <TableHead className="font-semibold hidden sm:table-cell">
                Status
              </TableHead>
              <TableHead className="text-right font-semibold">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {certificates.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  No certificates found.
                </TableCell>
              </TableRow>
            ) : (
              certificates.map((cert) => {
                const daysRemaining = getDaysUntilExpiry(cert.expiryDate);
                return (
                  <TableRow
                    key={cert.id}
                    className="hover:bg-muted/50 cursor-pointer"
                    onClick={() => handleRowClick(cert.id)}
                  >
                    <TableCell className="font-medium">
                      <div className="flex flex-col gap-1">
                        <div className="flex items-center gap-2">
                          {cert.isFavorite && (
                            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                          )}
                          <span className="font-medium">{cert.name}</span>
                        </div>
                        {/* Mobile: Show additional info when columns are hidden */}
                        <div className="sm:hidden text-xs text-muted-foreground">
                          {cert.issuingAuthority}
                        </div>
                        <div className="md:hidden sm:block text-xs text-muted-foreground">
                          {cert.certificateNumber}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      {cert.issuingAuthority}
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      {cert.certificateNumber}
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      {formatDate(cert.dateIssued)}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span
                          className={
                            cert.status === "expired"
                              ? "text-red-600 font-medium"
                              : ""
                          }
                        >
                          {formatDate(cert.expiryDate)}
                        </span>
                        {cert.status !== "expired" &&
                          daysRemaining !== null && (
                            <span
                              className={`text-xs ${
                                cert.status === "expiring-soon"
                                  ? "text-yellow-700"
                                  : "text-green-700"
                              }`}
                            >
                              {daysRemaining} days left
                            </span>
                          )}
                        {/* Mobile: Show issued date when hidden */}
                        <div className="lg:hidden text-xs text-muted-foreground">
                          Issued: {formatDate(cert.dateIssued)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      {getStatusBadge(cert.status)}
                    </TableCell>
                    <TableCell className="text-right">
                      <div
                        className="flex justify-end gap-1"
                        onClick={(e) => e.stopPropagation()}
                      >
                        {/* Mobile: Show only view button, hide others */}
                        <div className="sm:hidden">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-9 w-9"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleViewFiles(cert.id, e);
                            }}
                            disabled={!hasDownloadableDocument(cert)}
                            aria-label="View certificate file"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>

                        {/* Desktop: Show all action buttons */}
                        <div className="hidden sm:flex gap-1">
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleViewFiles(cert.id, e);
                                }}
                                disabled={!hasDownloadableDocument(cert)}
                                aria-label="View certificate file"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              {hasDownloadableDocument(cert)
                                ? "View file"
                                : "No file"}
                            </TooltipContent>
                          </Tooltip>

                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 hover:text-yellow-500"
                                onClick={(e) =>
                                  handleToggleFavorite(cert.id, e)
                                }
                                disabled={togglingFavoriteIds.has(cert.id)}
                                aria-label="Toggle favorite"
                              >
                                <Star
                                  className={`h-4 w-4 transition-colors ${
                                    cert.isFavorite
                                      ? "text-yellow-500 fill-yellow-500"
                                      : ""
                                  } ${
                                    togglingFavoriteIds.has(cert.id)
                                      ? "opacity-50"
                                      : ""
                                  }`}
                                />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              {togglingFavoriteIds.has(cert.id)
                                ? "Updating..."
                                : cert.isFavorite
                                ? "Remove from favorites"
                                : "Add to favorites"}
                            </TooltipContent>
                          </Tooltip>

                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDownload(cert.id);
                                }}
                                disabled={
                                  !hasDownloadableDocument(cert) ||
                                  downloadingIds.has(cert.id)
                                }
                                aria-label="Download certificate"
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              {downloadingIds.has(cert.id)
                                ? "Downloading..."
                                : hasDownloadableDocument(cert)
                                ? "Download"
                                : "No document available"}
                            </TooltipContent>
                          </Tooltip>

                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-red-600 hover:text-red-700"
                                onClick={(e) => handleDeleteClick(cert.id, e)}
                                aria-label="Delete certificate"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Delete certificate</TooltipContent>
                          </Tooltip>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>

      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleDelete}
        title={
          deletingCertificate
            ? certificates.find((c) => c.id === deletingCertificate)?.name ||
              "Certificate"
            : "Certificate"
        }
        isDeleting={isDeleting}
      />
    </TooltipProvider>
  );
}

"use client"

import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { AlertTriangle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

export default function AuthErrorPage() {
  const searchParams = useSearchParams()
  const [errorMessage, setErrorMessage] = useState<string>("An authentication error occurred")
  const [errorDescription, setErrorDescription] = useState<string>(
    "There was a problem with your authentication request. Please try again.",
  )

  useEffect(() => {
    const error = searchParams.get("error")

    if (error) {
      switch (error) {
        case "Configuration":
          setErrorMessage("Server Error")
          setErrorDescription("There is a problem with the server configuration. Please contact support.")
          break
        case "AccessDenied":
          setErrorMessage("Access Denied")
          setErrorDescription("You do not have permission to sign in.")
          break
        case "Verification":
          setErrorMessage("Verification Error")
          setErrorDescription("The verification link may have been used or is invalid.")
          break
        case "OAuthSignin":
        case "OAuthCallback":
        case "OAuthCreateAccount":
        case "EmailCreateAccount":
        case "Callback":
        case "OAuthAccountNotLinked":
        case "EmailSignin":
        case "CredentialsSignin":
          setErrorMessage("Sign In Error")
          setErrorDescription("There was a problem signing you in. Please check your credentials and try again.")
          break
        case "SessionRequired":
          setErrorMessage("Authentication Required")
          setErrorDescription("You must be signed in to access this page.")
          break
        default:
          setErrorMessage("Unknown Error")
          setErrorDescription("An unknown error occurred. Please try again.")
      }
    }
  }, [searchParams])

  return (
    <div className="container flex items-center justify-center min-h-screen py-12">
      <Card className="mx-auto max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-4">
            <AlertTriangle className="h-12 w-12 text-red-500" />
          </div>
          <CardTitle className="text-2xl text-center">{errorMessage}</CardTitle>
          <CardDescription className="text-center">{errorDescription}</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error Details</AlertTitle>
            <AlertDescription>
              Error Code: {searchParams.get("error") || "Unknown"}
              {searchParams.get("error_description") && <p className="mt-2">{searchParams.get("error_description")}</p>}
            </AlertDescription>
          </Alert>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <Button className="w-full" asChild>
            <Link href="/login">Return to Login</Link>
          </Button>
          <Button variant="outline" className="w-full" asChild>
            <Link href="/">Go to Home Page</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

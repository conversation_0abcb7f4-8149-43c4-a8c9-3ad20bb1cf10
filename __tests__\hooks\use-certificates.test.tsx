import { renderHook, act, waitFor } from "@testing-library/react";
import {
  useCertificates,
  useFilteredCertificates,
} from "@/hooks/use-certificates";
import {
  createMockCertificates,
  mockFetchSuccess,
  mockFetchError,
  cleanup,
} from "../utils/test-utils";

// Mock Next.js navigation
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
}));

describe("useCertificates", () => {
  beforeEach(() => {
    cleanup();
  });

  describe("Initial Load", () => {
    it("loads certificates on mount", async () => {
      const mockCertificates = createMockCertificates(3);
      mockFetchSuccess(mockCertificates);

      const { result } = renderHook(() => useCertificates());

      expect(result.current.isLoading).toBe(true);
      expect(result.current.certificates).toEqual([]);

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.certificates).toHaveLength(3);
      expect(result.current.error).toBeNull();
      expect(global.fetch).toHaveBeenCalledWith("/api/certificates", {
        credentials: "include",
      });
    });

    it("handles fetch errors", async () => {
      mockFetchError(500, "Server Error");

      const { result } = renderHook(() => useCertificates());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.certificates).toEqual([]);
      expect(result.current.error).toBe("Failed to fetch certificates");
    });

    it("handles unauthorized errors", async () => {
      mockFetchError(401, "Unauthorized");

      const { result } = renderHook(() => useCertificates());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.error).toBe("Unauthorized - please log in again");
    });

    it("skips initial load when initialLoad is false", () => {
      const { result } = renderHook(() =>
        useCertificates({ initialLoad: false })
      );

      expect(result.current.isLoading).toBe(false);
      expect(global.fetch).not.toHaveBeenCalled();
    });
  });

  describe("Certificate Operations", () => {
    it("adds a new certificate", async () => {
      const mockCertificates = createMockCertificates(2);
      mockFetchSuccess(mockCertificates);

      const { result } = renderHook(() => useCertificates());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Mock successful creation and refetch
      mockFetchSuccess({ id: "new-cert" });
      const updatedCertificates = [
        ...mockCertificates,
        createMockCertificates(1)[0],
      ];
      mockFetchSuccess(updatedCertificates);

      const newCertificate = {
        name: "New Certificate",
        issuingAuthority: "New Authority",
        certificateNumber: "NEW-001",
        dateIssued: new Date(),
        expiryDate: new Date(),
        documentUrl: null,
        notes: null,
        isFavorite: false,
        status: "active" as const,
      };

      await act(async () => {
        await result.current.addCertificate(newCertificate);
      });

      expect(global.fetch).toHaveBeenCalledWith("/api/certificates", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: expect.stringContaining('"name":"New Certificate"'),
      });
    });

    it("updates a certificate optimistically", async () => {
      const mockCertificates = createMockCertificates(2);
      mockFetchSuccess(mockCertificates);

      const { result } = renderHook(() => useCertificates());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      mockFetchSuccess({}); // Mock successful update

      const updates = { name: "Updated Certificate" };

      await act(async () => {
        await result.current.updateCertificate("cert-1", updates);
      });

      expect(result.current.certificates[0].name).toBe("Updated Certificate");
      expect(global.fetch).toHaveBeenCalledWith("/api/certificates/cert-1", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: expect.stringContaining('"name":"Updated Certificate"'),
      });
    });

    it("deletes a certificate optimistically", async () => {
      const mockCertificates = createMockCertificates(2);
      mockFetchSuccess(mockCertificates);

      const { result } = renderHook(() => useCertificates());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      mockFetchSuccess({}); // Mock successful deletion

      await act(async () => {
        await result.current.deleteCertificate("cert-1");
      });

      expect(result.current.certificates).toHaveLength(1);
      expect(
        result.current.certificates.find((c) => c.id === "cert-1")
      ).toBeUndefined();
      expect(global.fetch).toHaveBeenCalledWith("/api/certificates/cert-1", {
        method: "DELETE",
        credentials: "include",
      });
    });

    it("toggles favorite status optimistically", async () => {
      const mockCertificates = createMockCertificates(1);
      mockFetchSuccess(mockCertificates);

      const { result } = renderHook(() => useCertificates());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      const initialFavoriteStatus = result.current.certificates[0].isFavorite;
      mockFetchSuccess({ isFavorite: !initialFavoriteStatus });

      await act(async () => {
        await result.current.toggleFavorite("cert-1");
      });

      expect(result.current.certificates[0].isFavorite).toBe(
        !initialFavoriteStatus
      );
      expect(global.fetch).toHaveBeenCalledWith(
        "/api/certificates/cert-1/favorite",
        {
          method: "POST",
          credentials: "include",
        }
      );
    });
  });

  describe("Error Handling", () => {
    it("handles add certificate errors", async () => {
      const mockCertificates = createMockCertificates(1);
      mockFetchSuccess(mockCertificates);

      const { result } = renderHook(() => useCertificates());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      mockFetchError(400, "Validation Error");

      const newCertificate = {
        name: "",
        issuingAuthority: "",
        certificateNumber: "",
        dateIssued: new Date(),
        expiryDate: null,
        documentUrl: null,
        notes: null,
        isFavorite: false,
        status: "active" as const,
      };

      await expect(
        act(async () => {
          await result.current.addCertificate(newCertificate);
        })
      ).rejects.toThrow("Failed to create certificate");
    });

    it("handles update certificate errors", async () => {
      const mockCertificates = createMockCertificates(1);
      mockFetchSuccess(mockCertificates);

      const { result } = renderHook(() => useCertificates());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      mockFetchError(404, "Not Found");

      await expect(
        act(async () => {
          await result.current.updateCertificate("nonexistent", {
            name: "Updated",
          });
        })
      ).rejects.toThrow("Failed to update certificate");
    });
  });

  describe("Data Transformation", () => {
    it("transforms API response correctly", async () => {
      const apiResponse = [
        {
          id: "cert-1",
          name: "Test Certificate",
          issuingAuthority: "Test Authority",
          certificateNumber: "TEST-001",
          dateIssued: "2023-01-01T00:00:00.000Z",
          expiryDate: "2024-01-01T00:00:00.000Z",
          documentUrl: null,
          notes: null,
          isFavorite: false,
          createdAt: "2023-01-01T00:00:00.000Z",
          updatedAt: "2023-01-01T00:00:00.000Z",
          userId: "user-1",
        },
      ];

      mockFetchSuccess(apiResponse);

      const { result } = renderHook(() => useCertificates());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      const certificate = result.current.certificates[0];
      expect(certificate.dateIssued).toBeInstanceOf(Date);
      expect(certificate.expiryDate).toBeInstanceOf(Date);
      expect(certificate.createdAt).toBeInstanceOf(Date);
      expect(certificate.updatedAt).toBeInstanceOf(Date);
      expect(certificate.status).toBeDefined();
    });
  });
});

describe("useFilteredCertificates", () => {
  const mockCertificates = [
    createMockCertificates(1)[0], // active, favorite
    {
      ...createMockCertificates(1)[0],
      id: "cert-2",
      name: "Expiring Certificate",
      status: "expiring-soon" as const,
      isFavorite: false,
    },
    {
      ...createMockCertificates(1)[0],
      id: "cert-3",
      name: "Expired Certificate",
      status: "expired" as const,
      isFavorite: true,
    },
  ];

  describe("Filtering", () => {
    it("filters by all certificates", () => {
      const { result } = renderHook(() =>
        useFilteredCertificates(mockCertificates, "", "all", "name", "asc")
      );

      expect(result.current.filteredCertificates).toHaveLength(3);
      expect(result.current.counts.all).toBe(3);
    });

    it("filters by favorites", () => {
      const { result } = renderHook(() =>
        useFilteredCertificates(
          mockCertificates,
          "",
          "favorites",
          "name",
          "asc"
        )
      );

      expect(result.current.filteredCertificates).toHaveLength(2);
      expect(result.current.counts.favorites).toBe(2);
    });

    it("filters by expiring soon", () => {
      const { result } = renderHook(() =>
        useFilteredCertificates(
          mockCertificates,
          "",
          "expiring-soon",
          "name",
          "asc"
        )
      );

      expect(result.current.filteredCertificates).toHaveLength(1);
      expect(result.current.filteredCertificates[0].status).toBe(
        "expiring-soon"
      );
    });

    it("filters by expired", () => {
      const { result } = renderHook(() =>
        useFilteredCertificates(mockCertificates, "", "expired", "name", "asc")
      );

      expect(result.current.filteredCertificates).toHaveLength(1);
      expect(result.current.filteredCertificates[0].status).toBe("expired");
    });
  });

  describe("Search", () => {
    it("searches by certificate name", () => {
      const { result } = renderHook(() =>
        useFilteredCertificates(
          mockCertificates,
          "expiring",
          "all",
          "name",
          "asc"
        )
      );

      expect(result.current.filteredCertificates).toHaveLength(1);
      expect(result.current.filteredCertificates[0].name).toContain("Expiring");
    });

    it("searches by certificate number", () => {
      const { result } = renderHook(() =>
        useFilteredCertificates(
          mockCertificates,
          "TEST-001",
          "all",
          "name",
          "asc"
        )
      );

      expect(result.current.filteredCertificates).toHaveLength(1);
    });

    it("searches by issuing authority", () => {
      const { result } = renderHook(() =>
        useFilteredCertificates(
          mockCertificates,
          "Test Authority",
          "all",
          "name",
          "asc"
        )
      );

      expect(result.current.filteredCertificates).toHaveLength(3);
    });

    it("is case insensitive", () => {
      const { result } = renderHook(() =>
        useFilteredCertificates(
          mockCertificates,
          "EXPIRING",
          "all",
          "name",
          "asc"
        )
      );

      expect(result.current.filteredCertificates).toHaveLength(1);
    });
  });

  describe("Sorting", () => {
    it("sorts by name ascending", () => {
      const { result } = renderHook(() =>
        useFilteredCertificates(mockCertificates, "", "all", "name", "asc")
      );

      const names = result.current.filteredCertificates.map((c) => c.name);
      expect(names).toEqual([...names].sort());
    });

    it("sorts by name descending", () => {
      const { result } = renderHook(() =>
        useFilteredCertificates(mockCertificates, "", "all", "name", "desc")
      );

      const names = result.current.filteredCertificates.map((c) => c.name);
      expect(names).toEqual([...names].sort().reverse());
    });

    it("sorts by date issued", () => {
      const { result } = renderHook(() =>
        useFilteredCertificates(
          mockCertificates,
          "",
          "all",
          "dateIssued",
          "asc"
        )
      );

      const dates = result.current.filteredCertificates.map((c) =>
        c.dateIssued.getTime()
      );
      expect(dates).toEqual([...dates].sort((a, b) => a - b));
    });

    it("sorts by expiry date with null handling", () => {
      const certsWithNullExpiry = [
        ...mockCertificates,
        {
          ...mockCertificates[0],
          id: "cert-4",
          expiryDate: null,
        },
      ];

      const { result } = renderHook(() =>
        useFilteredCertificates(
          certsWithNullExpiry,
          "",
          "all",
          "expiryDate",
          "asc"
        )
      );

      // Certificates with null expiry should be at the end
      const lastCert =
        result.current.filteredCertificates[
          result.current.filteredCertificates.length - 1
        ];
      expect(lastCert.expiryDate).toBeNull();
    });
  });

  describe("Combined Operations", () => {
    it("applies search and filter together", () => {
      const { result } = renderHook(() =>
        useFilteredCertificates(
          mockCertificates,
          "Certificate",
          "favorites",
          "name",
          "asc"
        )
      );

      expect(result.current.filteredCertificates).toHaveLength(2);
      result.current.filteredCertificates.forEach((cert) => {
        expect(cert.isFavorite).toBe(true);
        expect(cert.name.toLowerCase()).toContain("certificate");
      });
    });

    it("applies search, filter, and sort together", () => {
      const { result } = renderHook(() =>
        useFilteredCertificates(
          mockCertificates,
          "Certificate",
          "all",
          "name",
          "desc"
        )
      );

      const names = result.current.filteredCertificates.map((c) => c.name);
      expect(names).toEqual([...names].sort().reverse());
    });
  });

  describe("Counts", () => {
    it("calculates correct counts", () => {
      const { result } = renderHook(() =>
        useFilteredCertificates(mockCertificates, "", "all", "name", "asc")
      );

      expect(result.current.counts).toEqual({
        all: 3,
        favorites: 2,
        expiringSoon: 1,
        expired: 1,
      });
    });
  });
});

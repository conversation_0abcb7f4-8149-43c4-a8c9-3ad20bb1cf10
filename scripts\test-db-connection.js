// Test database connection and schema
const fs = require("fs");
const path = require("path");
const { neon } = require("@neondatabase/serverless");

// Load environment variables manually
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, "..", ".env.local");
    const envContent = fs.readFileSync(envPath, "utf8");

    envContent.split("\n").forEach((line) => {
      const [key, ...valueParts] = line.split("=");
      if (key && valueParts.length > 0) {
        const value = valueParts.join("=").trim();
        process.env[key.trim()] = value;
      }
    });

    console.log("✅ Environment variables loaded");
  } catch (error) {
    console.error("❌ Failed to load .env.local:", error.message);
    process.exit(1);
  }
}

loadEnvFile();

async function testConnection() {
  try {
    // Use the correct connection string for Neon
    const connectionString =
      process.env.DATABASE_URL || process.env.POSTGRES_URL;
    console.log(
      "Using connection string:",
      connectionString ? "✅ Found" : "❌ Missing"
    );

    const sql = neon(connectionString);

    console.log("Testing database connection...");

    // Test basic connection
    const result = await sql`SELECT NOW() as current_time`;
    console.log("✅ Database connection successful:", result[0].current_time);

    // Check if Certificate table exists
    const tableCheck = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'Certificate'
      );
    `;
    console.log("✅ Certificate table exists:", tableCheck[0].exists);

    // Check current schema
    const schema = await sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'Certificate'
      ORDER BY ordinal_position;
    `;
    console.log("📋 Current Certificate table schema:");
    schema.forEach((col) => {
      console.log(
        `  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`
      );
    });

    // Check if isFavorite column exists
    const favoriteColumn = schema.find(
      (col) => col.column_name === "isFavorite"
    );
    if (!favoriteColumn) {
      console.log("⚠️  isFavorite column missing - will need to add it");

      // Add the isFavorite column
      console.log("🔧 Adding isFavorite column...");
      await sql`
        ALTER TABLE "Certificate"
        ADD COLUMN "isFavorite" boolean DEFAULT false NOT NULL;
      `;
      console.log("✅ isFavorite column added successfully");
    } else {
      console.log("✅ isFavorite column already exists");
    }

    // Test inserting a sample certificate
    console.log("🧪 Testing certificate operations...");

    const testCertId = "test-" + Date.now();
    const testUserId = "test-user-" + Date.now();

    // Insert test user first
    await sql`
      INSERT INTO "User" (id, email, name, "createdAt", "updatedAt")
      VALUES (${testUserId}, '<EMAIL>', 'Test User', NOW(), NOW())
      ON CONFLICT (id) DO NOTHING;
    `;

    // Insert test certificate
    await sql`
      INSERT INTO "Certificate" (
        id, name, "issuingAuthority", "certificateNumber",
        "dateIssued", "expiryDate", "isFavorite",
        "createdAt", "updatedAt", "userId"
      ) VALUES (
        ${testCertId},
        'Test Certificate',
        'Test Authority',
        'TEST-123',
        NOW(),
        NOW() + INTERVAL '1 year',
        true,
        NOW(),
        NOW(),
        ${testUserId}
      );
    `;
    console.log("✅ Test certificate inserted");

    // Query test certificate
    const testCert = await sql`
      SELECT * FROM "Certificate" WHERE id = ${testCertId};
    `;
    console.log("✅ Test certificate retrieved:", testCert[0]);

    // Clean up test data
    await sql`DELETE FROM "Certificate" WHERE id = ${testCertId};`;
    await sql`DELETE FROM "User" WHERE id = ${testUserId};`;
    console.log("✅ Test data cleaned up");

    console.log("\n🎉 Database is ready for the certificates application!");
  } catch (error) {
    console.error("❌ Database test failed:", error);
    process.exit(1);
  }
}

testConnection();

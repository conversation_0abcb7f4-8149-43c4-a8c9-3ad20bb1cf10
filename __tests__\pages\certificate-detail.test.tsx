import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import CertificateDetailPage from "@/app/(app)/certificates/[id]/page";

// Mock Next.js navigation
jest.mock("next/navigation", () => ({
  notFound: jest.fn(),
}));

// Mock Next.js headers
jest.mock("next/headers", () => ({
  cookies: jest.fn(),
}));

// Mock database function
jest.mock("@/lib/db", () => ({
  getCertificateById: jest.fn(),
}));

// Mock components that might not be available in test environment
jest.mock("@/components/certificate-status-badge", () => ({
  CertificateStatusBadge: ({ expiryDate }: any) => (
    <span data-testid="status-badge">
      {expiryDate ? "Active" : "No Expiry"}
    </span>
  ),
}));

describe("CertificateDetailPage - Database Integration", () => {
  const { cookies } = require("next/headers");
  const { getCertificateById } = require("@/lib/db");
  const { notFound } = require("next/navigation");

  const mockCertificate = {
    id: "UdLkNIzHonBrYgWerQjya",
    name: "Test Certificate",
    issuingAuthority: "Test Authority",
    certificateNumber: "TEST-001",
    dateIssued: new Date("2023-01-15"),
    expiryDate: new Date("2025-01-15"),
    documentUrl: "https://example.com/cert.pdf",
    notes: "Test notes",
    isFavorite: false,
    createdAt: new Date("2023-01-15"),
    updatedAt: new Date("2023-01-15"),
    userId: "user-1",
  };

  const mockUser = { id: "user-1", email: "<EMAIL>" };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock session cookie
    cookies.mockResolvedValue({
      get: jest.fn().mockReturnValue({
        value: JSON.stringify({ user: mockUser }),
      }),
    });
  });

  it("should render certificate details when valid ID is provided", async () => {
    getCertificateById.mockResolvedValue(mockCertificate);
    const mockParams = Promise.resolve({ id: "UdLkNIzHonBrYgWerQjya" });

    // Render the async component
    const component = await CertificateDetailPage({ params: mockParams });

    render(component);

    // Check if certificate details are rendered
    expect(screen.getByText("Test Certificate")).toBeInTheDocument();
    expect(screen.getByText("Certificate #TEST-001")).toBeInTheDocument();
    expect(screen.getByText("Test Authority")).toBeInTheDocument();
  });

  it("should handle awaited params correctly", async () => {
    const mockParamsWithId2 = Promise.resolve({ id: "2" });

    // Render the async component with different ID
    const component = await CertificateDetailPage({
      params: mockParamsWithId2,
    });

    render(component);

    // Check if the correct certificate is rendered
    expect(screen.getByText("Medical First Aid")).toBeInTheDocument();
    expect(screen.getByText("Certificate #MFA-2022-54321")).toBeInTheDocument();
    expect(screen.getByText("Maritime Medical Institute")).toBeInTheDocument();
  });

  it("should call notFound when certificate is not found", async () => {
    const { notFound } = require("next/navigation");
    const mockParamsInvalid = Promise.resolve({ id: "invalid-id" });

    // This should trigger notFound() call
    await expect(async () => {
      await CertificateDetailPage({ params: mockParamsInvalid });
    }).rejects.toThrow(); // notFound() throws an error in Next.js
  });

  it("should properly format dates", async () => {
    const component = await CertificateDetailPage({ params: mockParams });

    render(component);

    // Check if dates are properly formatted
    expect(screen.getByText(/January 15, 2023/)).toBeInTheDocument(); // Date issued
    expect(screen.getByText(/January 15, 2025/)).toBeInTheDocument(); // Expiry date
  });

  it("should display certificate status badge", async () => {
    const component = await CertificateDetailPage({ params: mockParams });

    render(component);

    // Check if status badge is rendered
    expect(screen.getByTestId("status-badge")).toBeInTheDocument();
  });

  it("should render action buttons correctly", async () => {
    const component = await CertificateDetailPage({ params: mockParams });

    render(component);

    // Check if action buttons are present
    expect(screen.getByText("Back to Certificates")).toBeInTheDocument();
    expect(screen.getByText("Edit Certificate")).toBeInTheDocument();
  });

  it("should handle certificate with no expiry date", async () => {
    // Mock a certificate with no expiry date
    const mockParamsNoExpiry = Promise.resolve({ id: "1" });

    const component = await CertificateDetailPage({
      params: mockParamsNoExpiry,
    });

    render(component);

    // The component should handle null expiry dates gracefully
    expect(screen.getByText("STCW Basic Safety Training")).toBeInTheDocument();
  });

  it("should calculate days remaining correctly", async () => {
    const component = await CertificateDetailPage({ params: mockParams });

    render(component);

    // Should display some form of expiry information
    // The exact text will depend on the current date vs expiry date
    expect(screen.getByText(/days/i)).toBeInTheDocument();
  });
});

describe("Next.js 15 Params Handling", () => {
  it("should properly await params before accessing properties", async () => {
    // Create a mock params promise
    let resolveParams: (value: { id: string }) => void;
    const paramsPromise = new Promise<{ id: string }>((resolve) => {
      resolveParams = resolve;
    });

    // Start rendering the component
    const componentPromise = CertificateDetailPage({ params: paramsPromise });

    // Resolve the params after a delay to simulate async behavior
    setTimeout(() => {
      resolveParams!({ id: "1" });
    }, 10);

    // Wait for the component to resolve
    const component = await componentPromise;

    render(component);

    // Verify the component rendered correctly
    expect(screen.getByText("STCW Basic Safety Training")).toBeInTheDocument();
  });

  it("should handle params promise rejection gracefully", async () => {
    const rejectedParams = Promise.reject(new Error("Params error"));

    // This should handle the rejection gracefully
    await expect(
      CertificateDetailPage({ params: rejectedParams })
    ).rejects.toThrow("Params error");
  });
});

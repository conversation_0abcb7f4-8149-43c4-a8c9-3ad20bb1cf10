"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Bell, Calendar, Check, FileWarning, Info } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";

export default function NotificationsPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock notification data
  const [notifications, setNotifications] = useState({
    unread: [
      {
        id: "1",
        title: "Certificate Expiring Soon",
        message:
          "Your GMDSS Radio Operator certificate will expire in 15 days.",
        type: "warning",
        date: new Date(new Date().getTime() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      },
      {
        id: "2",
        title: "Certificate Added",
        message:
          "Your STCW Basic Safety Training certificate has been added successfully.",
        type: "success",
        date: new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      },
      {
        id: "3",
        title: "Certificate Expired",
        message: "Your Ship Security Officer certificate has expired.",
        type: "error",
        date: new Date(new Date().getTime() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      },
    ],
    read: [
      {
        id: "4",
        title: "Welcome to Sealog",
        message:
          "Thank you for joining Sealog. Start by adding your first certificate.",
        type: "info",
        date: new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      },
      {
        id: "5",
        title: "Profile Updated",
        message: "Your profile information has been updated successfully.",
        type: "success",
        date: new Date(new Date().getTime() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
      },
    ],
  });

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await fetch("/api/certificates", {
          credentials: "include",
        });

        if (!response.ok) {
          if (response.status === 401) {
            router.push("/login");
            return;
          }
        }

        // Simulate loading notifications
        setTimeout(() => {
          setIsLoading(false);
        }, 500);
      } catch (error) {
        console.error("Auth check failed:", error);
        router.push("/login");
      }
    };

    checkAuth();
  }, [router]);

  const markAsRead = (id: string) => {
    const notification = notifications.unread.find((n) => n.id === id);
    if (notification) {
      setNotifications({
        unread: notifications.unread.filter((n) => n.id !== id),
        read: [notification, ...notifications.read],
      });
    }
  };

  const markAllAsRead = () => {
    setNotifications({
      unread: [],
      read: [...notifications.unread, ...notifications.read],
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "warning":
        return <Calendar className="h-5 w-5 text-amber-500" />;
      case "success":
        return <Check className="h-5 w-5 text-green-500" />;
      case "error":
        return <FileWarning className="h-5 w-5 text-red-500" />;
      case "info":
      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 text-center">
        <div className="animate-pulse">Loading notifications...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="mt-4 text-center">
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6 md:p-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
          <p className="text-muted-foreground">
            Stay updated with important alerts about your certificates
          </p>
        </div>
        <div className="flex items-center gap-2">
          {notifications.unread.length > 0 && (
            <Button variant="outline" onClick={markAllAsRead}>
              Mark All as Read
            </Button>
          )}
          <Button variant="outline">
            <Bell className="mr-2 h-4 w-4" /> Notification Settings
          </Button>
        </div>
      </div>

      <Tabs defaultValue="unread">
        <TabsList>
          <TabsTrigger value="unread" className="relative">
            Unread
            {notifications.unread.length > 0 && (
              <Badge className="ml-2 bg-primary">
                {notifications.unread.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="all">All Notifications</TabsTrigger>
        </TabsList>
        <TabsContent value="unread" className="mt-4">
          {notifications.unread.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Bell className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-lg font-medium">No unread notifications</p>
                <p className="text-muted-foreground">You're all caught up!</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {notifications.unread.map((notification) => (
                <Card
                  key={notification.id}
                  className="relative overflow-hidden"
                >
                  <div className="absolute left-0 top-0 bottom-0 w-1 bg-primary" />
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div className="flex items-center gap-3">
                        <div className="rounded-full bg-muted p-2">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <CardTitle className="text-lg">
                          {notification.title}
                        </CardTitle>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => markAsRead(notification.id)}
                      >
                        Mark as Read
                      </Button>
                    </div>
                    <CardDescription>
                      {formatDate(notification.date)}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p>{notification.message}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
        <TabsContent value="all" className="mt-4">
          <div className="space-y-4">
            {[...notifications.unread, ...notifications.read].length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Bell className="h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-lg font-medium">No notifications</p>
                  <p className="text-muted-foreground">
                    You don't have any notifications yet
                  </p>
                </CardContent>
              </Card>
            ) : (
              [...notifications.unread, ...notifications.read].map(
                (notification) => (
                  <Card
                    key={notification.id}
                    className="relative overflow-hidden"
                  >
                    <div
                      className={`absolute left-0 top-0 bottom-0 w-1 ${
                        notifications.unread.some(
                          (n) => n.id === notification.id
                        )
                          ? "bg-primary"
                          : "bg-muted-foreground"
                      }`}
                    />
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center gap-3">
                          <div className="rounded-full bg-muted p-2">
                            {getNotificationIcon(notification.type)}
                          </div>
                          <CardTitle className="text-lg">
                            {notification.title}
                          </CardTitle>
                        </div>
                        {notifications.unread.some(
                          (n) => n.id === notification.id
                        ) && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => markAsRead(notification.id)}
                          >
                            Mark as Read
                          </Button>
                        )}
                      </div>
                      <CardDescription>
                        {formatDate(notification.date)}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p>{notification.message}</p>
                    </CardContent>
                  </Card>
                )
              )
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

# Testing Guide for Optimized Certificates System

## Overview

This document provides comprehensive testing coverage for the optimized certificates system, including file upload functionality, client-side performance optimizations, and end-to-end user flows.

## Test Structure

```
__tests__/
├── utils/
│   └── test-utils.tsx          # Shared testing utilities and mocks
├── components/
│   └── file-upload-fallback.test.tsx  # File upload component tests
├── hooks/
│   └── use-certificates.test.tsx      # Data management hooks tests
├── pages/
│   └── certificates.test.tsx          # Main certificates page tests
└── integration/
    ├── file-upload.test.tsx           # File upload integration tests
    └── certificates-flow.test.tsx     # End-to-end flow tests
```

## Installation and Setup

### 1. Install Testing Dependencies

```bash
pnpm install --save-dev @testing-library/jest-dom @testing-library/react @testing-library/user-event @types/jest jest jest-environment-jsdom
```

### 2. Run Tests

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage

# Run tests for CI
pnpm test:ci
```

## Test Categories

### 1. File Upload Testing (`__tests__/components/file-upload-fallback.test.tsx`)

**Coverage:**
- ✅ File type validation (PDF, JPG, PNG)
- ✅ File size validation (10MB limit)
- ✅ Drag and drop functionality
- ✅ Click to upload functionality
- ✅ Keyboard accessibility (Enter/Space keys)
- ✅ Error handling and display
- ✅ File removal functionality
- ✅ Disabled state handling
- ✅ ARIA attributes and accessibility
- ✅ Mobile responsiveness

**Key Test Cases:**
```javascript
// File type validation
it('accepts valid file types (PDF, JPG, PNG)', () => {
  // Test implementation
})

// File size validation
it('rejects files larger than 10MB', () => {
  // Test implementation
})

// Drag and drop
it('handles file drop with valid file', () => {
  // Test implementation
})

// Accessibility
it('has proper ARIA attributes', () => {
  // Test implementation
})
```

### 2. Data Management Testing (`__tests__/hooks/use-certificates.test.tsx`)

**Coverage:**
- ✅ Initial data loading
- ✅ Error handling (network errors, 401 unauthorized)
- ✅ CRUD operations (Create, Read, Update, Delete)
- ✅ Optimistic updates
- ✅ Client-side filtering and sorting
- ✅ Search functionality
- ✅ Data transformation from API responses

**Key Test Cases:**
```javascript
// Data loading
it('loads certificates on mount', async () => {
  // Test implementation
})

// Client-side filtering
it('filters by certificate status', () => {
  // Test implementation
})

// Optimistic updates
it('updates certificate optimistically', async () => {
  // Test implementation
})
```

### 3. Page Component Testing (`__tests__/pages/certificates.test.tsx`)

**Coverage:**
- ✅ Loading states with skeleton UI
- ✅ Error states and recovery
- ✅ Search functionality with debouncing
- ✅ Filter action cards
- ✅ View toggle (grid/table)
- ✅ Sorting controls
- ✅ Keyboard shortcuts (Ctrl+K, Ctrl+N)
- ✅ URL state management
- ✅ Empty states
- ✅ Performance characteristics

**Key Test Cases:**
```javascript
// Loading states
it('shows skeleton when loading', () => {
  // Test implementation
})

// Search functionality
it('debounces search input', async () => {
  // Test implementation
})

// Performance
it('does not make API calls when filtering locally', async () => {
  // Test implementation
})
```

### 4. File Upload Integration Testing (`__tests__/integration/file-upload.test.tsx`)

**Coverage:**
- ✅ Uploadthing integration
- ✅ File upload success and error handling
- ✅ Form validation with file uploads
- ✅ Certificate creation with file URLs
- ✅ Loading states during upload
- ✅ Authentication error handling
- ✅ Date handling in forms
- ✅ Mobile responsiveness

**Key Test Cases:**
```javascript
// Uploadthing integration
it('uploads file successfully and saves URL', async () => {
  // Test implementation
})

// Form validation
it('validates required fields before allowing submission', async () => {
  // Test implementation
})

// Error handling
it('handles upload errors gracefully', async () => {
  // Test implementation
})
```

### 5. End-to-End Flow Testing (`__tests__/integration/certificates-flow.test.tsx`)

**Coverage:**
- ✅ Complete user journey (view, filter, search, navigate)
- ✅ Performance with large datasets
- ✅ Accessibility compliance
- ✅ URL state management
- ✅ Error recovery
- ✅ Keyboard navigation
- ✅ Mobile responsiveness

**Key Test Cases:**
```javascript
// Complete flow
it('allows user to view, filter, search, and navigate certificates', async () => {
  // Test implementation
})

// Performance
it('handles large datasets efficiently', async () => {
  // Test implementation
})

// Accessibility
it('supports keyboard navigation', async () => {
  // Test implementation
})
```

## Test Utilities

### Mock Data Factories

```javascript
// Create mock certificates
const mockCertificates = createMockCertificates(5)

// Create mock files
const mockFile = createMockFile('test.pdf', 'application/pdf', 1024 * 1024)

// Mock API responses
mockFetchSuccess(data)
mockFetchError(status, message)
```

### Event Simulation

```javascript
// Drag and drop events
const dragEvent = createMockDragEvent([file])

// Keyboard events
const keyEvent = createMockKeyboardEvent('Enter')
```

## Performance Testing

### Metrics Tracked

1. **Initial Load Time**: Page render to interactive
2. **Search Response Time**: Input to filtered results
3. **Filter Response Time**: Click to updated view
4. **Memory Usage**: Large dataset handling
5. **API Call Frequency**: Ensuring client-side operations

### Performance Assertions

```javascript
// Search should be fast
expect(searchDuration).toBeLessThan(100) // 100ms for 1000 items

// No additional API calls for local operations
expect(global.fetch).not.toHaveBeenCalled()

// Total render time should be reasonable
expect(totalDuration).toBeLessThan(1000) // 1 second
```

## Accessibility Testing

### ARIA Compliance

- ✅ Proper roles and labels
- ✅ Keyboard navigation support
- ✅ Screen reader compatibility
- ✅ Focus management
- ✅ Error announcements

### Keyboard Shortcuts

- ✅ `Ctrl+K`: Focus search input
- ✅ `Ctrl+N`: Navigate to new certificate
- ✅ `Enter/Space`: Activate upload area
- ✅ `Tab`: Navigate through interface

## Mobile Testing

### Responsive Breakpoints

- ✅ 320px (small mobile)
- ✅ 375px (standard mobile)
- ✅ 414px (large mobile)
- ✅ 768px+ (tablet/desktop)

### Touch Interactions

- ✅ Minimum 44px touch targets
- ✅ Drag and drop on touch devices
- ✅ Swipe gestures (if applicable)
- ✅ Pinch to zoom compatibility

## Coverage Goals

### Target Coverage Metrics

- **Lines**: 70%+
- **Functions**: 70%+
- **Branches**: 70%+
- **Statements**: 70%+

### Critical Path Coverage

- ✅ Certificate data loading: 100%
- ✅ File upload flow: 100%
- ✅ Search and filtering: 100%
- ✅ Error handling: 90%+
- ✅ User interactions: 90%+

## Running Specific Test Suites

```bash
# Run only file upload tests
pnpm test file-upload

# Run only integration tests
pnpm test integration

# Run only component tests
pnpm test components

# Run tests matching pattern
pnpm test certificates

# Run tests with verbose output
pnpm test --verbose
```

## Debugging Tests

### Common Issues and Solutions

1. **Mock not working**: Check jest.setup.js configuration
2. **Async operations failing**: Use waitFor() and proper async/await
3. **DOM queries failing**: Verify component rendering and selectors
4. **File upload mocks**: Ensure FileReader and File APIs are mocked

### Debug Commands

```bash
# Run single test file
pnpm test file-upload-fallback.test.tsx

# Run with debug output
pnpm test --verbose --no-coverage

# Run specific test case
pnpm test -t "uploads file successfully"
```

## Continuous Integration

### CI Configuration

The tests are configured to run in CI environments with:

- ✅ No watch mode
- ✅ Coverage reporting
- ✅ JUnit XML output
- ✅ Fail on coverage threshold
- ✅ Parallel test execution

### CI Command

```bash
pnpm test:ci
```

This comprehensive testing suite ensures the optimized certificates system works correctly across all user scenarios, maintains performance standards, and provides a reliable user experience.

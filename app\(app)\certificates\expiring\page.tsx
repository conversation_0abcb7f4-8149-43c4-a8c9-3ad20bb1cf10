"use client";

import Link from "next/link";
import { Edit, Eye, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CertificateStatusBadge } from "@/components/certificate-status-badge";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function ExpiringCertificatesPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [certificates, setCertificates] = useState<any[]>([]);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await fetch("/api/certificates", {
          credentials: "include",
        });

        if (!response.ok) {
          if (response.status === 401) {
            router.push("/login");
            return;
          }
        }

        setIsLoading(false);
      } catch (error) {
        console.error("Auth check failed:", error);
        router.push("/login");
      }
    };

    checkAuth();

    // For demo purposes, we'll use hardcoded data
    try {
      // Sample data
      const expiredCertificates = [
        {
          id: "6",
          name: "Ship Security Officer",
          issuingAuthority: "Maritime Security Institute",
          certificateNumber: "SSO-2022-13579",
          expiryDate: new Date(new Date().getTime() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
          status: "expired",
        },
      ];

      const expiringSoon30Days = [
        {
          id: "5",
          name: "GMDSS Radio Operator",
          issuingAuthority: "Communications Authority",
          certificateNumber: "GMDSS-2021-87654",
          expiryDate: new Date(new Date().getTime() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
          status: "expiring30",
        },
      ];

      const expiringSoon90Days = [
        {
          id: "2",
          name: "Medical First Aid",
          issuingAuthority: "Maritime Medical Institute",
          certificateNumber: "MFA-2022-54321",
          expiryDate: new Date(new Date().getTime() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
          status: "expiring90",
        },
      ];

      const allCertificates = [
        ...expiredCertificates,
        ...expiringSoon30Days,
        ...expiringSoon90Days,
      ];

      setCertificates(allCertificates);
      setIsLoading(false);
    } catch (err) {
      console.error("Error loading certificates:", err);
      setError("Failed to load certificates. Please try again.");
      setIsLoading(false);
    }
  }, [router]);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getDaysRemaining = (expiryDate: Date) => {
    const today = new Date();
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getDaysRemainingText = (expiryDate: Date) => {
    const days = getDaysRemaining(expiryDate);
    if (days < 0) return `Expired ${Math.abs(days)} days ago`;
    if (days === 0) return "Expires today";
    return `${days} days remaining`;
  };

  const getDaysRemainingClass = (expiryDate: Date) => {
    const days = getDaysRemaining(expiryDate);
    if (days < 0) return "text-red-500";
    if (days <= 30) return "text-orange-500";
    if (days <= 90) return "text-yellow-500";
    return "text-green-500";
  };

  const CertificateCard = ({ certificate }: { certificate: any }) => (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg">{certificate.name}</CardTitle>
          <CertificateStatusBadge expiryDate={certificate.expiryDate} />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="text-sm text-muted-foreground">
            <span className="font-medium">Issuing Authority:</span>{" "}
            {certificate.issuingAuthority}
          </div>
          <div className="text-sm text-muted-foreground">
            <span className="font-medium">Certificate Number:</span>{" "}
            {certificate.certificateNumber}
          </div>
          <div className="text-sm">
            <span className="font-medium">Expiry Date:</span>{" "}
            {formatDate(certificate.expiryDate)}
          </div>
          <div
            className={`text-sm font-medium ${getDaysRemainingClass(
              certificate.expiryDate
            )}`}
          >
            {getDaysRemainingText(certificate.expiryDate)}
          </div>
          <div className="flex gap-2 mt-4">
            <Button size="sm" variant="outline" asChild>
              <Link href={`/certificates/${certificate.id}`}>
                <Eye className="mr-1 h-3 w-3" /> View
              </Link>
            </Button>
            <Button size="sm" asChild>
              <Link href={`/certificates/${certificate.id}/edit`}>
                <Edit className="mr-1 h-3 w-3" /> Renew
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (isLoading) {
    return (
      <div className="p-6 text-center">
        <div className="animate-pulse">Loading certificates...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="mt-4 text-center">
          <Button asChild>
            <Link href="/dashboard">Return to Dashboard</Link>
          </Button>
        </div>
      </div>
    );
  }

  // Filter certificates by status
  const expiredCertificates = certificates.filter(
    (cert) => cert.status === "expired"
  );
  const expiringSoon30Days = certificates.filter(
    (cert) => cert.status === "expiring30"
  );
  const expiringSoon90Days = certificates.filter(
    (cert) => cert.status === "expiring90"
  );

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Expiring Certificates
          </h1>
          <p className="text-muted-foreground">
            Certificates that need your attention
          </p>
        </div>
        <Button asChild>
          <Link href="/certificates/new">
            <Plus className="mr-2 h-4 w-4" /> Add New Certificate
          </Link>
        </Button>
      </div>

      {expiredCertificates.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-red-500">Expired</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {expiredCertificates.map((certificate) => (
              <CertificateCard key={certificate.id} certificate={certificate} />
            ))}
          </div>
        </div>
      )}

      {expiringSoon30Days.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-orange-500">
            Expiring in Next 30 Days
          </h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {expiringSoon30Days.map((certificate) => (
              <CertificateCard key={certificate.id} certificate={certificate} />
            ))}
          </div>
        </div>
      )}

      {expiringSoon90Days.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-yellow-500">
            Expiring in 31-90 Days
          </h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {expiringSoon90Days.map((certificate) => (
              <CertificateCard key={certificate.id} certificate={certificate} />
            ))}
          </div>
        </div>
      )}

      {certificates.length === 0 && (
        <div className="text-center py-12">
          <div className="text-lg font-medium">
            No expiring certificates found
          </div>
          <p className="text-muted-foreground mt-2">
            All your certificates are valid for more than 90 days.
          </p>
          <Button className="mt-4" asChild>
            <Link href="/certificates">View All Certificates</Link>
          </Button>
        </div>
      )}
    </div>
  );
}

-- Migration script to add CertificateFile table for multi-file support
-- This script should be run against your database to add the new table

-- Create the CertificateFile table
CREATE TABLE IF NOT EXISTS "CertificateFile" (
  "id" TEXT PRIMARY KEY,
  "certificateId" TEXT NOT NULL,
  "fileName" TEXT NOT NULL,
  "fileUrl" TEXT NOT NULL,
  "fileSize" INTEGER NOT NULL,
  "fileType" TEXT NOT NULL,
  "uploadthingKey" TEXT,
  "uploadOrder" INTEGER DEFAULT 0 NOT NULL,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  FOREIGN KEY ("certificateId") REFERENCES "Certificate"("id") ON DELETE CASCADE
);

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS "idx_certificate_files_certificate_id" ON "CertificateFile"("certificateId");
CREATE INDEX IF NOT EXISTS "idx_certificate_files_upload_order" ON "CertificateFile"("uploadOrder");

-- Optional: Migrate existing single files to the new table structure
-- Uncomment the following lines if you want to migrate existing data

/*
INSERT INTO "CertificateFile" (
  "id",
  "certificateId", 
  "fileName",
  "fileUrl",
  "fileSize",
  "fileType",
  "uploadthingKey",
  "uploadOrder",
  "createdAt"
)
SELECT 
  'legacy-' || "id" as "id",
  "id" as "certificateId",
  COALESCE("documentName", 'certificate.pdf') as "fileName",
  "documentUrl" as "fileUrl",
  CASE 
    WHEN "documentSize" IS NOT NULL THEN 
      CAST(REPLACE(REPLACE("documentSize", ' KB', ''), ' MB', '000') AS INTEGER) * 1024
    ELSE 0 
  END as "fileSize",
  COALESCE("documentType", 'application/pdf') as "fileType",
  NULL as "uploadthingKey",
  0 as "uploadOrder",
  "createdAt"
FROM "Certificate" 
WHERE "documentUrl" IS NOT NULL AND "documentUrl" != '';
*/

-- Note: The legacy fields (documentUrl, documentName, documentSize, documentType) 
-- are kept for backward compatibility and will be automatically populated 
-- with the first file when creating new certificates.

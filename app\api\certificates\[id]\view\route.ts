import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { getCertificateById } from "@/lib/db"

// Helper function to get user from session cookie
async function getUserFromSession() {
  try {
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get("session")
    if (!sessionCookie) {
      return null
    }

    const session = JSON.parse(sessionCookie.value)
    return session.user
  } catch (error) {
    console.error("Error parsing session:", error)
    return null
  }
}

// GET /api/certificates/[id]/view - Get certificate document URL for viewing
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = await params
    const certificate = await getCertificateById(id, user.id)
    
    if (!certificate) {
      return NextResponse.json({ error: "Certificate not found" }, { status: 404 })
    }

    if (!certificate.documentUrl) {
      return NextResponse.json({ error: "No document available for this certificate" }, { status: 404 })
    }

    // Return the file URL and metadata for viewing
    return NextResponse.json({
      viewUrl: certificate.documentUrl,
      fileName: certificate.documentName || `certificate-${certificate.name}.pdf`,
      fileSize: certificate.documentSize,
      fileType: certificate.documentType,
      certificateName: certificate.name
    })
  } catch (error) {
    console.error("Error getting certificate view URL:", error)
    return NextResponse.json(
      { error: "Failed to get certificate view URL" },
      { status: 500 }
    )
  }
}

// Script to remove all login-alt references and replace with proper authentication
const fs = require('fs');
const path = require('path');

const filesToUpdate = [
  'app/(app)/help/page.tsx',
  'app/(app)/profile/page.tsx', 
  'app/(app)/notifications/page.tsx',
  'app/(app)/settings/page.tsx',
  'app/(app)/certificates/expiring/page.tsx',
  'app/(app)/certificates/recent/page.tsx',
  'app/(app)/certificates/new/page.tsx'
];

function updateFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Replace login-alt redirects with proper API authentication check
    const oldAuthPattern = /\/\/ Check if the session cookie exists\s+const hasCookie = document\.cookie\.split\(";"\)\.some\(\(item\) => item\.trim\(\)\.startsWith\("session="\)\)\s+if \(!hasCookie\) \{\s+router\.push\("\/login-alt"\)\s+return\s+\}/g;
    
    const newAuthCode = `// Check authentication with API call
    const checkAuth = async () => {
      try {
        const response = await fetch("/api/certificates", {
          credentials: "include",
        });
        
        if (!response.ok) {
          if (response.status === 401) {
            router.push("/login");
            return;
          }
        }
        
        setIsLoading(false);
      } catch (error) {
        console.error("Auth check failed:", error);
        router.push("/login");
      }
    };
    
    checkAuth();`;
    
    // Replace the old authentication pattern
    content = content.replace(oldAuthPattern, newAuthCode);
    
    // Replace any remaining login-alt references
    content = content.replace(/\/login-alt/g, '/login');
    
    // Replace logout handlers that redirect to login-alt
    content = content.replace(
      /window\.location\.href = "\/login-alt"/g, 
      'window.location.href = "/login"'
    );
    
    // Write the updated content back
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Updated: ${filePath}`);
    
  } catch (error) {
    console.error(`❌ Failed to update ${filePath}:`, error.message);
  }
}

console.log('🔧 Removing login-alt references from components...\n');

filesToUpdate.forEach(updateFile);

console.log('\n✅ All files updated!');
console.log('\nNote: Some files may need manual review for proper async/await patterns.');

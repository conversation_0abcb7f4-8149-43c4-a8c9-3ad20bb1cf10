import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { getCertificateFiles, getCertificateById } from "@/lib/db"

// Helper function to get user from session cookie
async function getUserFromSession() {
  try {
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get("session")
    if (!sessionCookie) {
      return null
    }

    const session = JSON.parse(sessionCookie.value)
    return session.user
  } catch (error) {
    console.error("Error parsing session:", error)
    return null
  }
}

// Helper function to sanitize filename for ZIP
function sanitizeFileName(fileName: string): string {
  return fileName.replace(/[<>:"/\\|?*]/g, '_')
}

// Helper function to fetch file as buffer
async function fetchFileAsBuffer(url: string): Promise<Buffer> {
  const response = await fetch(url)
  if (!response.ok) {
    throw new Error(`Failed to fetch file: ${response.statusText}`)
  }
  const arrayBuffer = await response.arrayBuffer()
  return Buffer.from(arrayBuffer)
}

// GET /api/certificates/[id]/download-zip - Get file URLs for client-side ZIP creation
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getUserFromSession()
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = await params

    // Verify certificate exists and belongs to user
    const certificate = await getCertificateById(id, user.id)
    if (!certificate) {
      return NextResponse.json({ error: "Certificate not found" }, { status: 404 })
    }

    // Get all files for this certificate
    let files = await getCertificateFiles(id, user.id)

    // If no files in new table, check legacy fields
    if (files.length === 0 && certificate.documentUrl) {
      files = [{
        id: `legacy-${id}`,
        certificateId: id,
        fileName: certificate.documentName || 'certificate.pdf',
        fileUrl: certificate.documentUrl,
        fileSize: certificate.documentSize ? parseInt(certificate.documentSize.replace(/[^\d]/g, '')) * 1024 : 0,
        fileType: certificate.documentType || 'application/pdf',
        uploadthingKey: null,
        uploadOrder: 0,
        createdAt: certificate.createdAt,
      }]
    }

    if (files.length === 0) {
      return NextResponse.json({ error: "No files found for this certificate" }, { status: 404 })
    }

    // Return file metadata for client-side ZIP creation
    const sanitizedCertName = sanitizeFileName(certificate.name)
    const zipFileName = `Certificate_${sanitizedCertName}_Files.zip`

    return NextResponse.json({
      certificateName: certificate.name,
      zipFileName,
      files: files.map(file => ({
        id: file.id,
        fileName: sanitizeFileName(file.fileName),
        fileUrl: file.fileUrl,
        fileSize: file.fileSize,
        fileType: file.fileType,
      }))
    })

  } catch (error) {
    console.error("Error preparing ZIP download:", error)
    return NextResponse.json(
      { error: "Failed to prepare ZIP download" },
      { status: 500 }
    )
  }
}

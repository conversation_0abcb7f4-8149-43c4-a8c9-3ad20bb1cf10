"use client";

import type React from "react";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { AppSidebar } from "@/components/app-sidebar";
import { AppTopbar } from "@/components/app-topbar";
import { logger } from "@/lib/logger";

export default function AppLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  useEffect(() => {
    const checkAuthentication = async () => {
      logger.componentMount("AppLayout");
      const endTimer = logger.authStart("layout-auth-check");

      try {
        // Test API call to verify authentication instead of checking cookies
        logger.debug("auth", "Testing authentication with API call");
        const response = await fetch("/api/certificates", {
          credentials: "include",
        });

        if (!response.ok) {
          if (response.status === 401) {
            logger.authFailure(
              "layout-auth-check",
              "Unauthorized - redirecting to login"
            );
            logger.navigationStart("app-layout", "/login");
            router.push("/login");
            return;
          }
          throw new Error(`API check failed: ${response.status}`);
        }

        logger.authSuccess("layout-auth-check", "authenticated-user");
        setIsAuthenticated(true);
        setIsLoading(false);
        endTimer();
      } catch (err) {
        logger.error("auth", "Authentication check failed", {
          error: err instanceof Error ? err.message : "Unknown error",
        });
        logger.navigationStart("app-layout", "/login");
        router.push("/login");
        endTimer();
      }
    };

    checkAuthentication();
  }, [router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-pulse">Loading...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Don't render anything while redirecting
  }

  return (
    <div className="flex min-h-screen">
      {/* Desktop Sidebar - Only visible on md+ screens */}
      <div
        className={`hidden md:block fixed left-0 top-0 h-screen z-30 transition-all duration-300 ${
          isSidebarCollapsed ? "w-16" : "w-64"
        }`}
      >
        <AppSidebar
          isCollapsed={isSidebarCollapsed}
          onCollapseChange={setIsSidebarCollapsed}
        />
      </div>

      {/* Mobile Sidebar - Overlay/Sheet component handled in AppSidebar */}
      <div className="md:hidden">
        <AppSidebar isCollapsed={false} onCollapseChange={() => {}} />
      </div>

      {/* Main content area */}
      <div
        className={`flex-1 flex flex-col transition-all duration-300 ${
          // On mobile: full width, on desktop: account for sidebar
          "w-full md:ml-16 md:w-[calc(100%-4rem)]"
        } ${
          // On desktop, adjust for expanded sidebar
          !isSidebarCollapsed ? "md:ml-64 md:w-[calc(100%-16rem)]" : ""
        }`}
      >
        {/* Topbar */}
        <header className="sticky top-0 z-20 h-16 border-b bg-background px-4 md:px-6">
          <AppTopbar />
        </header>

        {/* Page content */}
        <main className="flex-1 w-full">{children}</main>
      </div>
    </div>
  );
}

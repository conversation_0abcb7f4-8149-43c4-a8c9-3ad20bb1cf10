import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export function CertificateFormSkeleton() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container max-w-4xl mx-auto p-4 sm:p-6 space-y-6">
        {/* Header with Actions */}
        <div className="flex flex-col sm:flex-row gap-3 justify-between items-start sm:items-center">
          <Skeleton className="h-10 w-48" />
          <Skeleton className="h-11 w-44" />
        </div>

        {/* Main Form */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Certificate Information */}
          <div className="lg:col-span-2 space-y-6">
            <Card className="shadow-lg border-0 bg-card/50 backdrop-blur">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-9 w-9 rounded-lg" />
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-48" />
                    <Skeleton className="h-4 w-64" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Certificate Name */}
                <div className="space-y-3">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-12 w-full" />
                </div>

                {/* Issuing Authority */}
                <div className="space-y-3">
                  <Skeleton className="h-5 w-36" />
                  <Skeleton className="h-12 w-full" />
                </div>

                {/* Certificate Number */}
                <div className="space-y-3">
                  <Skeleton className="h-5 w-40" />
                  <Skeleton className="h-12 w-full" />
                </div>

                {/* Date Fields */}
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-3">
                    <Skeleton className="h-5 w-24" />
                    <Skeleton className="h-12 w-full" />
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-5 w-24" />
                      <div className="flex items-center space-x-2">
                        <Skeleton className="h-4 w-4" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                    </div>
                    <Skeleton className="h-12 w-full" />
                  </div>
                </div>

                {/* Notes */}
                <div className="space-y-3">
                  <Skeleton className="h-5 w-28" />
                  <Skeleton className="h-32 w-full" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* File Upload Sidebar */}
          <div className="space-y-4">
            <Card className="shadow-lg border-0 bg-card/50 backdrop-blur">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-7 w-7 rounded-md" />
                  <div>
                    <Skeleton className="h-5 w-24" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                {/* Upload Area Skeleton */}
                <div className="border-2 border-dashed rounded-lg p-4 text-center">
                  <div className="flex flex-col items-center space-y-3">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-28" />
                      <Skeleton className="h-3 w-40" />
                    </div>
                    <Skeleton className="h-8 w-20" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

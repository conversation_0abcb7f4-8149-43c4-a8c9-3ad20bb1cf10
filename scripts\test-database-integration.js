// Test database integration for certificates
const fs = require('fs');
const path = require('path');

// Load environment variables
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local');
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim().replace(/^"(.*)"$/, '$1');
        process.env[key.trim()] = value;
      }
    });
  } catch (error) {
    console.error('Failed to load .env.local:', error.message);
  }
}

loadEnvFile();

async function testDatabaseIntegration() {
  console.log('🧪 Testing Database Integration for Certificates');
  console.log('===============================================\n');

  try {
    // Import our database functions
    const {
      getCertificatesWithFilters,
      createCertificate,
      updateCertificate,
      toggleCertificateFavorite,
      deleteCertificate,
      getCertificateById
    } = require('../lib/db.ts');

    const { neon } = require("@neondatabase/serverless");
    const sql = neon(process.env.DATABASE_URL_UNPOOLED || process.env.DATABASE_URL);
    
    const testUserId = 'test-user-' + Date.now();
    
    console.log('1️⃣ Setting up test user...');
    
    // Create test user
    await sql`
      INSERT INTO "User" (id, email, name, "createdAt", "updatedAt")
      VALUES (${testUserId}, '<EMAIL>', 'Test User', NOW(), NOW());
    `;
    console.log('✅ Test user created:', testUserId);

    console.log('\n2️⃣ Testing certificate creation...');
    
    // Test creating certificates with our database functions
    const cert1Id = 'cert-1-' + Date.now();
    const cert2Id = 'cert-2-' + Date.now();
    const cert3Id = 'cert-3-' + Date.now();
    
    await createCertificate({
      id: cert1Id,
      name: 'STCW Basic Safety Training',
      issuingAuthority: 'Maritime Safety Authority',
      certificateNumber: 'BST-2023-12345',
      dateIssued: new Date('2023-01-15'),
      expiryDate: new Date('2025-01-15'),
      isFavorite: true,
      userId: testUserId
    });
    
    await createCertificate({
      id: cert2Id,
      name: 'Medical First Aid',
      issuingAuthority: 'Maritime Medical Institute',
      certificateNumber: 'MFA-2022-54321',
      dateIssued: new Date('2022-08-22'),
      expiryDate: new Date('2024-08-22'),
      isFavorite: false,
      userId: testUserId
    });
    
    await createCertificate({
      id: cert3Id,
      name: 'Advanced Firefighting',
      issuingAuthority: 'Maritime Safety Authority',
      certificateNumber: 'AFF-2021-45678',
      dateIssued: new Date('2021-09-05'),
      expiryDate: new Date('2023-09-05'), // Expired
      isFavorite: false,
      userId: testUserId
    });
    
    console.log('✅ Created 3 test certificates');

    console.log('\n3️⃣ Testing getCertificatesWithFilters...');
    
    // Test getting all certificates
    const allCerts = await getCertificatesWithFilters(testUserId);
    console.log(`✅ Retrieved ${allCerts.length} certificates`);
    
    // Test filtering by favorites
    const favoriteCerts = await getCertificatesWithFilters(testUserId, { filter: 'favorites' });
    console.log(`✅ Retrieved ${favoriteCerts.length} favorite certificates`);
    
    // Test search functionality
    const searchResults = await getCertificatesWithFilters(testUserId, { search: 'STCW' });
    console.log(`✅ Search for 'STCW' returned ${searchResults.length} certificates`);
    
    // Test sorting
    const sortedByName = await getCertificatesWithFilters(testUserId, { 
      sortBy: 'name', 
      sortOrder: 'asc' 
    });
    console.log(`✅ Sorted by name: ${sortedByName.map(c => c.name).join(', ')}`);
    
    // Test expired filter
    const expiredCerts = await getCertificatesWithFilters(testUserId, { filter: 'expired' });
    console.log(`✅ Retrieved ${expiredCerts.length} expired certificates`);

    console.log('\n4️⃣ Testing certificate operations...');
    
    // Test getting individual certificate
    const individualCert = await getCertificateById(cert1Id, testUserId);
    console.log(`✅ Retrieved individual certificate: ${individualCert?.name}`);
    
    // Test updating certificate
    await updateCertificate(cert2Id, testUserId, {
      notes: 'Updated with test notes',
      isFavorite: true
    });
    console.log('✅ Updated certificate with notes and favorite status');
    
    // Test toggling favorite
    const toggleResult = await toggleCertificateFavorite(cert3Id, testUserId);
    console.log(`✅ Toggled favorite status: ${toggleResult.isFavorite}`);
    
    // Verify the changes
    const updatedCert2 = await getCertificateById(cert2Id, testUserId);
    const updatedCert3 = await getCertificateById(cert3Id, testUserId);
    console.log(`✅ Verified updates - Cert2 favorite: ${updatedCert2?.isFavorite}, Cert3 favorite: ${updatedCert3?.isFavorite}`);

    console.log('\n5️⃣ Testing status computation...');
    
    // Test the status computation function from the frontend
    function computeCertificateStatus(expiryDate) {
      if (!expiryDate) return "active";
      
      const today = new Date();
      const diffTime = expiryDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays < 0) return "expired";
      if (diffDays <= 90) return "expiring-soon";
      return "active";
    }
    
    const certsWithStatus = allCerts.map(cert => ({
      ...cert,
      status: computeCertificateStatus(cert.expiryDate)
    }));
    
    console.log('Certificate statuses:');
    certsWithStatus.forEach(cert => {
      console.log(`  - ${cert.name}: ${cert.status}`);
    });

    console.log('\n6️⃣ Testing filtering by computed status...');
    
    // Test filtering by expiring soon (certificates expiring within 90 days)
    const expiringSoonCerts = await getCertificatesWithFilters(testUserId, { filter: 'expiring-soon' });
    console.log(`✅ Retrieved ${expiringSoonCerts.length} expiring-soon certificates`);

    console.log('\n7️⃣ Testing deletion...');
    
    // Test deleting a certificate
    await deleteCertificate(cert3Id, testUserId);
    console.log('✅ Deleted certificate');
    
    // Verify deletion
    const remainingCerts = await getCertificatesWithFilters(testUserId);
    console.log(`✅ Remaining certificates: ${remainingCerts.length}`);

    console.log('\n🧹 Cleaning up test data...');
    await sql`DELETE FROM "Certificate" WHERE "userId" = ${testUserId};`;
    await sql`DELETE FROM "User" WHERE id = ${testUserId};`;
    console.log('✅ Test data cleaned up');

    console.log('\n🎉 Database integration test completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Certificate creation works');
    console.log('✅ Filtering and search work');
    console.log('✅ Sorting works');
    console.log('✅ Favorites system works');
    console.log('✅ Status computation works');
    console.log('✅ CRUD operations work');
    console.log('\n🚀 Ready for frontend integration!');

  } catch (error) {
    console.error('\n❌ Database integration test failed:', error);
    console.error('Stack trace:', error.stack);
  }
}

testDatabaseIntegration();
